import React, { useState } from 'react';
import { Search, Mail, Shield, Zap } from 'lucide-react';
import { AnalysisFormData } from '../types';
import LoadingSpinner from './LoadingSpinner';

interface AnalysisFormProps {
  onSubmit: (data: AnalysisFormData) => Promise<void>;
  isLoading: boolean;
}

const AnalysisForm: React.FC<AnalysisFormProps> = ({ onSubmit, isLoading }) => {
  const [formData, setFormData] = useState<AnalysisFormData>({
    contentType: '',
    searchLimit: 'limited',
    searchCount: 50,
    blockedKeyword: '',
    email: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.contentType.trim()) {
      newErrors.contentType = 'Content type is required';
    }

    if (formData.searchLimit === 'limited') {
      if (!formData.searchCount || formData.searchCount < 1 || formData.searchCount > 100) {
        newErrors.searchCount = 'Search count must be between 1 and 100';
      }
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      newErrors.email = 'Invalid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? (value ? parseInt(value, 10) : '') : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (validateForm()) {
      await onSubmit(formData);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-4">
              New Analysis
            </h2>
            <p className="text-gray-400 mb-6">
              Fill out the form below to start analyzing YouTube content.
            </p>
          </div>

          <div className="space-y-4">
            {/* Content Type */}
            <div>
              <label htmlFor="contentType" className="block text-sm font-medium text-gray-300 mb-2">
                Content Type <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  id="contentType"
                  name="contentType"
                  value={formData.contentType}
                  onChange={handleInputChange}
                  className="input-field pl-10 w-full text-white"
                  placeholder="e.g., Minecraft Tutorials, Cooking Videos"
                />
              </div>
              {errors.contentType && (
                <p className="mt-1 text-sm text-red-500">{errors.contentType}</p>
              )}
            </div>

            {/* Search Limit */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Search Limit
              </label>
              <div className="flex gap-4">
                <label className="relative flex items-center">
                  <input
                    type="radio"
                    name="searchLimit"
                    value="limited"
                    checked={formData.searchLimit === 'limited'}
                    onChange={e => handleInputChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                    className="sr-only"
                  />
                  <div className={`w-4 h-4 border rounded-full mr-2 transition-colors ${
                    formData.searchLimit === 'limited'
                      ? 'bg-red-500 border-red-500'
                      : 'border-gray-400'
                  }`}>
                    {formData.searchLimit === 'limited' && (
                      <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                    )}
                  </div>
                  <span className="text-gray-300">Limited</span>
                </label>
                <label className="relative flex items-center">
                  <input
                    type="radio"
                    name="searchLimit"
                    value="unlimited"
                    checked={formData.searchLimit === 'unlimited'}
                    onChange={e => handleInputChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                    className="sr-only"
                  />
                  <div className={`w-4 h-4 border rounded-full mr-2 transition-colors ${
                    formData.searchLimit === 'unlimited'
                      ? 'bg-red-500 border-red-500'
                      : 'border-gray-400'
                  }`}>
                    {formData.searchLimit === 'unlimited' && (
                      <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                    )}
                  </div>
                  <span className="text-gray-300">Unlimited</span>
                </label>
              </div>
            </div>

            {formData.searchLimit === 'limited' && (
              <div>
                <label htmlFor="searchCount" className="block text-sm font-medium text-gray-300 mb-2">
                  Search Count
                </label>
                <div className="relative">
                  <Zap className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="number"
                    id="searchCount"
                    name="searchCount"
                    value={formData.searchCount}
                    onChange={handleInputChange}
                    min="1"
                    max="100"
                    className="input-field pl-10 w-full text-white"
                  />
                </div>
                {errors.searchCount && (
                  <p className="mt-1 text-sm text-red-500">{errors.searchCount}</p>
                )}
              </div>
            )}

            {/* Blocked Keywords */}
            <div>
              <label htmlFor="blockedKeyword" className="block text-sm font-medium text-gray-300 mb-2">
                Blocked Keyword
              </label>
              <div className="relative">
                <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  id="blockedKeyword"
                  name="blockedKeyword"
                  value={formData.blockedKeyword}
                  onChange={handleInputChange}
                  className="input-field pl-10 w-full text-white"
                  placeholder="Optional: Enter keywords to exclude"
                />
              </div>
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                Email <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="input-field pl-10 w-full text-white"
                  placeholder="Enter your email address"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-500">{errors.email}</p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="button-primary w-full flex items-center justify-center"
          >
            {isLoading ? (
              <LoadingSpinner className="w-5 h-5" />
            ) : (
              <>
                <Search className="w-5 h-5 mr-2" />
                Start Analysis
              </>
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default AnalysisForm;