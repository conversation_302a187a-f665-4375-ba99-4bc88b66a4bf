import { useState, useCallback } from 'react';
import { BarChart3, Link, Youtube } from 'lucide-react';
import AnalysisForm from './components/AnalysisForm';
import SavedLinks from './components/SavedLinks';
import SuccessModal from './components/SuccessModal';
import Toast from './components/Toast';
import { AnalysisFormData, ToastNotification } from './types';
import { saveLink } from './utils/storage';

type Tab = 'analysis' | 'saved';

function App() {
  const [activeTab, setActiveTab] = useState<Tab>('analysis');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [lastAnalysisTitle, setLastAnalysisTitle] = useState('');
  const [spreadsheetUrl, setSpreadsheetUrl] = useState<string>('');
  const [toasts, setToasts] = useState<ToastNotification[]>([]);

  const showToast = useCallback((type: ToastNotification['type'], message: string) => {
    const id = crypto.randomUUID();
    const newToast: ToastNotification = { id, type, message };
    setToasts(prev => [...prev, newToast]);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const handleFormSubmit = async (data: AnalysisFormData) => {
    setIsLoading(true);
    
    try {
      const webhookUrl = 'https://n8n-maximizeai-n8n.duckdns.org/webhook-test/197da747-a0d1-4775-898b-bd01d953af5a';
      
      const payload = {
        contentType: data.contentType,
        searchLimit: data.searchLimit,
        searchCount: data.searchLimit === 'limited' ? data.searchCount : null,
        blockedKeyword: data.blockedKeyword || null,
        email: data.email,
        timestamp: new Date().toISOString(),
        source: 'youtube-analytics-platform'
      };

      console.log('Sending payload to n8n:', payload);

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(payload),
        mode: 'cors'
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (response.ok) {
        const responseData = await response.text();
        console.log('Response data:', responseData);
        
        setLastAnalysisTitle(data.contentType);
        const mockSpreadsheetUrl = `https://docs.google.com/spreadsheets/d/analysis-${Date.now()}`;
        setSpreadsheetUrl(mockSpreadsheetUrl);
        setShowSuccessModal(true);
        showToast('success', 'Analysis request sent successfully! You will receive results via email.');
        
        saveLink(data.contentType, mockSpreadsheetUrl);
      } else {
        const errorText = await response.text();
        console.error('Webhook error response:', errorText);
        throw new Error(`Webhook returned ${response.status}: ${errorText || response.statusText}`);
      }
    } catch (error) {
      console.error('Error submitting to n8n webhook:', error);
      
      if (error instanceof TypeError && error.message.includes('fetch')) {
        showToast('error', 'Network error: Unable to connect to the analysis service. Please check your internet connection.');
      } else if (error instanceof Error && error.message.includes('CORS')) {
        showToast('error', 'CORS error: The analysis service is not properly configured for web requests.');
      } else {
        showToast('error', `Failed to start analysis: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0C0C0C] via-[#481E14] to-[#9B3922] relative overflow-hidden">
      {/* Animated background elements */}
      <div className="animated-bg">
        <div className="animated-circle"></div>
        <div className="animated-circle"></div>
        <div className="animated-circle"></div>
      </div>
      
      {/* Noise overlay for texture */}
      <div className="noise-overlay"></div>
      
      <div className="relative z-10">
        {/* Header with glass effect */}
        <header className="bg-white/5 backdrop-blur-md border-b border-white/10 sticky top-0">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center gap-2">
                <Youtube className="text-red-500 w-8 h-8" />
                <h1 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  YouTube Research
                </h1>
              </div>
              
              <nav className="flex space-x-4">
                <button
                  onClick={() => setActiveTab('analysis')}
                  className={`nav-tab ${activeTab === 'analysis' ? 'active' : ''}`}
                >
                  <BarChart3 className="w-5 h-5" />
                  <span>Analysis</span>
                </button>
                <button
                  onClick={() => setActiveTab('saved')}
                  className={`nav-tab ${activeTab === 'saved' ? 'active' : ''}`}
                >
                  <Link className="w-5 h-5" />
                  <span>Saved</span>
                </button>
              </nav>
            </div>
          </div>
        </header>

        {/* Main content with animation */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fade-in">
          <div className="card p-6 animate-slide-up">
            {activeTab === 'analysis' ? (
              <AnalysisForm onSubmit={handleFormSubmit} isLoading={isLoading} />
            ) : (
              <SavedLinks onShowToast={showToast} />
            )}
          </div>
        </main>
      </div>

      {/* Modals and Toasts */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        spreadsheetUrl={spreadsheetUrl}
        analysisTitle={lastAnalysisTitle}
      />
      
      <div className="fixed bottom-4 right-4 z-50 space-y-2">
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            notification={toast}
            onRemove={removeToast}
          />
        ))}
      </div>
    </div>
  );
}

export default App;