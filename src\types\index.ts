export type SearchType = 'limited' | 'unlimited';

export interface AnalysisFormData {
  contentType: string;
  blockedKeyword?: string;
  email?: string;
  searchType: SearchType;
  searchLimit: number;
  limited: boolean;
  searchCount: number;
}

export interface SavedLink {
  id: string;
  title: string;
  url: string;
  createdAt: string;
  expiresAt: string;
  spreadsheetUrl?: string;
}

export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
}