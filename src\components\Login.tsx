import React, { useState } from 'react';
import Cookies from 'js-cookie';

interface LoginProps {
  onLogin: () => void;
}

const WEBHOOK_URL = 'https://n8n-maximizeai-n8n.duckdns.org/webhook-test/d6082a9b-47d0-492d-be2c-02df3670fa81';

const Login: React.FC<LoginProps> = ({ onLogin }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);
    try {
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });
      if (response.status === 201) {
        Cookies.set('ytresearch_logged_in', 'true', { expires: 7 });
        onLogin();
      } else if (response.status === 300) {
        setError('Incorrect username.');
      } else if (response.status === 301) {
        setError('Incorrect password.');
      } else {
        setError('Unexpected error. Please try again.');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-zinc-900">
      <div className="w-full max-w-sm">
        <div className="mb-6 text-center text-sm text-gray-400">
          <div>Need a little help? Try using a username that starts with <span className="font-mono text-yellow-300">Red</span> and ends with <span className="font-mono text-yellow-300">shell</span>.</div>
          <div>And for the password, maybe something similar but starting with <span className="font-mono text-yellow-300">Mr</span>!</div>
        </div>
        <form onSubmit={handleSubmit} className="bg-white/10 p-8 rounded-xl shadow-lg w-full border border-white/10">
          <h2 className="text-2xl font-bold text-yellow-300 mb-6 text-center">Login</h2>
          <div className="mb-4">
            <label className="block text-white/80 mb-2" htmlFor="username">Username</label>
            <input
              id="username"
              type="text"
              className="w-full px-3 py-2 rounded bg-zinc-800 text-white border border-white/10 focus:outline-none focus:ring-2 focus:ring-yellow-300"
              value={username}
              onChange={e => setUsername(e.target.value)}
              required
              autoComplete="username"
            />
          </div>
          <div className="mb-6">
            <label className="block text-white/80 mb-2" htmlFor="password">Password</label>
            <input
              id="password"
              type="password"
              className="w-full px-3 py-2 rounded bg-zinc-800 text-white border border-white/10 focus:outline-none focus:ring-2 focus:ring-yellow-300"
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
              autoComplete="current-password"
            />
          </div>
          {error && <div className="mb-4 text-red-400 text-center">{error}</div>}
          <button
            type="submit"
            className="w-full bg-yellow-400 text-gray-900 font-bold py-2 rounded hover:bg-yellow-300 transition-colors disabled:opacity-60"
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Login; 