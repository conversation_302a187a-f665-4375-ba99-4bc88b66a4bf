import React, { useState } from 'react';
import { Youtube, Instagram, ArrowU<PERSON>, Link, Play } from 'lucide-react';
import GrowthComparisonChart from './GrowthComparisonChart';

const DashboardPage: React.FC = () => {
  const [activeChartTab, setActiveChartTab] = useState('YouTube');
  // Placeholder data - replace with actual data fetching later
  const dashboardData = {
    date: 'June 16, 2025',
    lastUpdated: '1:29 PM',
    youtube: {
      subscribers: '145.7K',
      growth: 7.2,
      videos: 87,
    },
    tiktok: {
      followers: '28.6K',
      growth: 3.8,
      posts: 124,
    },
    instagram: {
      followers: '52.4K',
      growth: 6.5,
      posts: 158,
    },
  };

  return (
    <div className="flex flex-col p-8 text-white">
      {/* Demo Warning Banner */}
      <div className="bg-yellow-500 text-black text-center py-2 px-4 font-bold text-sm sm:text-base mb-8 rounded-lg">
        🚧 This is a demo version
      </div>
      
      {/* Header */}
      <div className="flex items-center mb-6">
        <h1 className="text-4xl font-bold mr-4">Hello Redshell</h1>
        <span className="bg-yellow-500/20 text-yellow-300 text-sm font-semibold px-3 py-1 rounded-full flex items-center gap-1">
          <span className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></span>
          CHANNEL ANALYTICS
        </span>
      </div>
      <p className="text-white/70 text-sm mb-8">
        <span className="w-2 h-2 bg-white/70 rounded-full inline-block mr-2"></span>
        {dashboardData.date} Last updated: {dashboardData.lastUpdated}
      </p>

      {/* Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        {/* YouTube Card */}
        <div className="card p-6 flex flex-col items-start">
          <div className="flex items-center justify-between w-full mb-4">
            <Youtube className="w-8 h-8 text-red-500" />
            <div className="flex items-center text-green-400 font-semibold">
              <ArrowUp className="w-4 h-4 mr-1" />
              {dashboardData.youtube.growth}%
            </div>
          </div>
          <p className="text-white/70 text-sm">Subscribers</p>
          <p className="text-4xl font-bold mb-2">{dashboardData.youtube.subscribers}</p>
          <p className="text-white/70 text-sm">Videos</p>
          <p className="text-xl font-semibold">{dashboardData.youtube.videos}</p>
        </div>

        {/* TikTok Card */}
        <div className="card p-6 flex flex-col items-start">
          <div className="flex items-center justify-between w-full mb-4">
            <Play className="w-8 h-8 text-black bg-white rounded-full" />
            <div className="flex items-center text-green-400 font-semibold">
              <ArrowUp className="w-4 h-4 mr-1" />
              {dashboardData.tiktok.growth}%
            </div>
          </div>
          <p className="text-white/70 text-sm">Followers</p>
          <p className="text-4xl font-bold mb-2">{dashboardData.tiktok.followers}</p>
          <p className="text-white/70 text-sm">Posts</p>
          <p className="text-xl font-semibold">{dashboardData.tiktok.posts}</p>
        </div>

        {/* Instagram Card */}
        <div className="card p-6 flex flex-col items-start">
          <div className="flex items-center justify-between w-full mb-4">
            <Instagram className="w-8 h-8 text-pink-500" />
            <div className="flex items-center text-green-400 font-semibold">
              <ArrowUp className="w-4 h-4 mr-1" />
              {dashboardData.instagram.growth}%
            </div>
          </div>
          <p className="text-white/70 text-sm">Followers</p>
          <p className="text-4xl font-bold mb-2">{dashboardData.instagram.followers}</p>
          <p className="text-white/70 text-sm">Posts</p>
          <p className="text-xl font-semibold">{dashboardData.instagram.posts}</p>
        </div>
      </div>

      {/* Growth Comparison Chart */}
      <div className="card p-6">
        <h2 className="text-xl font-bold mb-4">Growth Comparison</h2>
        <p className="text-white/70 text-sm mb-6">Followers growth across platforms</p>
        {/* <div className="flex border-b border-white/20 mb-6">
          <button 
            className={`px-4 py-2 font-medium relative ${activeChartTab === 'YouTube' ? 'text-white border-b-2 border-orange-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveChartTab('YouTube')}
          >
            YouTube
          </button>
          <button 
            className={`px-4 py-2 font-medium relative ${activeChartTab === 'LinkedIn' ? 'text-white border-b-2 border-orange-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveChartTab('LinkedIn')}
          >
            LinkedIn
          </button>
          <button 
            className={`px-4 py-2 font-medium relative ${activeChartTab === 'Instagram' ? 'text-white border-b-2 border-orange-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveChartTab('Instagram')}
          >
            Instagram
          </button>
        </div> */}
        {/* Chart Component */}
        <GrowthComparisonChart />
      </div>
    </div>
  );
};

export default DashboardPage; 