import React, { useState, useEffect, useRef } from 'react';
import ABTestPanel from './ABTestPanel';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, Cell, Line, CartesianGrid } from 'recharts';
import ReactDOM from 'react-dom';
import <PERSON> from 'papaparse';
import { Trash2 } from 'lucide-react';
import { analyzeContentPerformance, VideoData as AnalysisVideoData, ContentFormat, AnalysisResult, getContentFormat } from '../logic/analyzeContentPerformance';

// Use the same endpoint as in App.tsx
const N8N_ENDPOINT = 'https://n8n-maximizeai-n8n.duckdns.org/webhook-test/197da747-a0d1-4775-898b-bd01d953af5a';

// Type for a video object (adjust as needed for your data)
type VideoData = {
  channel?: string;
  title?: string;
  views?: number;
  likes?: number;
  comments?: number;
  engagementRate?: number;
  tags?: string[];
  [key: string]: any;
};

const kpiFields = [
  { title: 'Videos Analyzed', get: (data: VideoData[]) => data.length },
  { title: 'Avg. Views', get: (data: VideoData[]) => data.length ? Math.round(data.reduce((sum, v) => sum + Number(v.views || 0), 0) / data.length).toLocaleString() : 0 },
  { title: 'Avg. Engagement', get: (data: VideoData[]) => data.length ? (data.reduce((sum, v) => sum + Number(v.engagementRate || 0), 0) / data.length).toFixed(2) + '%' : '0%' },
  { title: 'Top Video Views', get: (data: VideoData[]) => data.length ? Math.max(...data.map(v => Number(v.views || 0))).toLocaleString() : 0 },
];

const tabs = [
  { label: 'Video Patterns' },
  { label: 'Content Formats' },
  { label: 'Top Performers' },
  { label: 'Performance Comparator' },
  // { label: 'Content Repurposing Ideas' },
  // { label: 'Channel Performance' }, // Removed Tab
];

const AnalysisDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [data] = useState<VideoData[]>(() => {
    const saved = localStorage.getItem('dashboardData');
    return saved ? JSON.parse(saved) : [];
  });
  // Filter/sort state
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('views');
  const [modalIndex, setModalIndex] = useState<number | null>(null);
  const [showVideo, setShowVideo] = useState<number | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<string>('All Channels');
  const [importMessage, setImportMessage] = useState<string | null>(null);
  const [importSuccess, setImportSuccess] = useState<boolean | null>(null);
  const [lastDeletedVideo, setLastDeletedVideo] = useState<any | null>(null);
  const [undoTimer, setUndoTimer] = useState<number>(0);
  const undoTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [topFormatFilter, setTopFormatFilter] = useState<ContentFormat | 'All'>('All');

  // New state for scroll-based animation
  const [isScrolledPastHero, setIsScrolledPastHero] = useState(false);
  const scrollSentinelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (scrollSentinelRef.current) {
        const rect = scrollSentinelRef.current.getBoundingClientRect();
        // Adjust this threshold as needed. 0 means when the top of the sentinel passes the viewport top.
        // A negative value means it has scrolled X pixels above the viewport top.
        // 100 means when it's 100px from the top of the viewport.
        setIsScrolledPastHero(rect.top <= 100); 
      }
    };

    window.addEventListener('scroll', handleScroll);
    // Call once on mount to set initial state
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Hardcoded list of popular games
  const GAME_LIST = [
    'Minecraft', 'Fortnite', 'Roblox', 'Valorant', 'GTA', 'League of Legends', 'Apex Legends', 'Call of Duty', 'PUBG', 'Among Us', 'Overwatch', 'CSGO', 'Terraria', 'Rocket League', 'FIFA', 'Pokemon', 'Zelda', 'Mario', 'Elden Ring', 'Brawl Stars'
  ];

  // Assign each video to 'Games' if any tag matches a game, otherwise 'Other'
  let videoCategoryMap: Record<number, string> = {};
  data.forEach((v, idx) => {
    let tags: string[] = [];
    if (Array.isArray(v.tags)) tags = v.tags;
    else if (typeof v.tags === 'string') {
      try {
        const parsed = JSON.parse(v.tags);
        if (Array.isArray(parsed)) tags = parsed;
        else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
      } catch {
        tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
      }
    }
    const isGame = tags.some(tag => GAME_LIST.some(game => tag.toLowerCase().includes(game.toLowerCase())));
    videoCategoryMap[idx] = isGame ? 'Games' : 'Other';
  });

  // Always show both categories: Games and Other
  const categoriesWithData = ['Games', 'Other'];

  // Filtering
  let filtered = data;
  if (selectedCategory !== 'All') {
    filtered = filtered.filter((v, idx) => videoCategoryMap[idx] === selectedCategory);
  }
  // Keyword/tag search filter
  if (searchKeyword.trim() !== '') {
    const kw = searchKeyword.trim().toLowerCase();
    filtered = filtered.filter(v => {
      // Check tags
      let tags: string[] = [];
      if (Array.isArray(v.tags)) tags = v.tags;
      else if (typeof v.tags === 'string') {
        try {
          const parsed = JSON.parse(v.tags);
          if (Array.isArray(parsed)) tags = parsed;
          else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
        } catch {
          tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
        }
      }
      const tagMatch = tags.some(tag => tag.toLowerCase().includes(kw));
      const titleMatch = (v.title || '').toLowerCase().includes(kw);
      const descMatch = (v.description || '').toLowerCase().includes(kw);
      return tagMatch || titleMatch || descMatch;
    });
  }
  // Get unique channel names from filtered videos
  const channelNames = Array.from(new Set(filtered.map(v => v.channelName || v.channel).filter(Boolean)));
  // Further filter by channel if selected
  let channelFiltered = filtered;
  if (selectedCategory !== 'All' && selectedChannel !== 'All Channels') {
    channelFiltered = filtered.filter(v => (v.channelName || v.channel) === selectedChannel);
  }
  if (selectedDate) {
    const selected = new Date(selectedDate);
    filtered = filtered.filter(v => {
      if (!v.publishedDate) return false;
      const d = new Date(v.publishedDate);
      return d >= selected;
    });
  }

  // Sorting
  filtered = [...filtered].sort((a, b) => {
    if (sortBy === 'views') return Number(b.views || 0) - Number(a.views || 0);
    if (sortBy === 'engagement') return Number(b.engagementRate || 0) - Number(a.engagementRate || 0);
    if (sortBy === 'recency') {
      return new Date(b.publishedDate || 0).getTime() - new Date(a.publishedDate || 0).getTime();
    }
    return 0;
  });

  // Use channelFiltered if a specific channel is selected, otherwise filtered
  const contentSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;

  // --- Video Length Distribution ---
  const lengthBuckets = { Short: 0, Medium: 0, Long: 0 };
  contentSource.forEach(v => {
    let duration = v.duration;
    if (typeof duration === 'string') {
      // Try to parse as seconds if string
      const asNum = Number(duration);
      if (!isNaN(asNum)) duration = asNum;
      // Optionally: parse ISO 8601 duration here if needed
    }
    if (typeof duration === 'number') {
      if (duration < 240) lengthBuckets.Short++;
      else if (duration <= 1200) lengthBuckets.Medium++;
      else lengthBuckets.Long++;
    }
  });
  const totalVideos = lengthBuckets.Short + lengthBuckets.Medium + lengthBuckets.Long;
  const pieData = [
    { label: 'Short', value: lengthBuckets.Short, color: 'from-yellow-400/60 to-red-400/60' },
    { label: 'Medium', value: lengthBuckets.Medium, color: 'from-orange-400/60 to-yellow-400/60' },
    { label: 'Long', value: lengthBuckets.Long, color: 'from-red-400/60 to-orange-400/60' },
  ];

  // --- Upload Days ---
  const dayCounts: Record<string, number> = { Mon: 0, Tue: 0, Wed: 0, Thu: 0, Fri: 0, Sat: 0, Sun: 0 };
  contentSource.forEach(v => {
    const dateStr = v.publishedDate;
    if (dateStr) {
      const d = new Date(dateStr);
      if (!isNaN(d.getTime())) {
        const day = d.getUTCDay(); // 0=Sun, 1=Mon, ...
        const dayNames = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'];
        const dayName = dayNames[day];
        if (dayName in dayCounts) dayCounts[dayName]++;
      }
    }
  });
  const weekDays = ['Mon','Tue','Wed','Thu','Fri','Sat','Sun'];

  // Reset selectedChannel if category changes
  useEffect(() => {
    setSelectedChannel('All Channels');
  }, [selectedCategory]);

  // Export filtered data as CSV
  const handleExportCSV = () => {
    const exportSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
    if (!exportSource.length) return;
    // Remove publishedDate, spreadsheetUrl, and metrics from each row
    const exportData = exportSource.map(({ publishedDate, spreadsheetUrl, metrics, ...rest }) => rest);
    const csv = Papa.unparse(exportData);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'youtube_analysis_export.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Import CSV and merge with dashboardData
  const handleImportCSV = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results: Papa.ParseResult<any>) => {
        try {
          const imported = results.data;
          console.log('Imported CSV data:', imported);
          if (!Array.isArray(imported) || imported.length === 0) {
            setImportMessage('No data found in the uploaded CSV. Please export from this system and try again.');
            setImportSuccess(false);
            return;
          }
          // Only keep rows with a youtubeUrl
          const validImported = imported.filter((item: any) => item.youtubeUrl && typeof item.youtubeUrl === 'string');
          console.log('Valid imported rows:', validImported);
          if (validImported.length === 0) {
            setImportMessage('No valid rows found in the uploaded CSV. Please ensure your file includes a "youtubeUrl" column and matches the exported format.');
            setImportSuccess(false);
            return;
          }
          const saved = localStorage.getItem('dashboardData');
          const current = saved ? JSON.parse(saved) : [];
          // Merge and deduplicate by youtubeUrl
          const merged = [...current, ...validImported].reduce((acc: any[], item: any) => {
            if (!item.youtubeUrl) return acc;
            if (!acc.some(v => v.youtubeUrl === item.youtubeUrl)) acc.push(item);
            return acc;
          }, []);
          console.log('Merged data:', merged);
          const addedCount = merged.length - current.length;
          localStorage.setItem('dashboardData', JSON.stringify(merged));
          if (addedCount > 0) {
            setImportMessage(`Data imported successfully! ${addedCount} new row(s) added. The page will now reload.`);
            setImportSuccess(true);
            setTimeout(() => window.location.reload(), 1500);
          } else {
            setImportMessage('No new data was added (all rows were duplicates or already present).');
            setImportSuccess(false);
          }
        } catch (err) {
          setImportMessage('Failed to import data. Please ensure your CSV matches the exported format.');
          setImportSuccess(false);
        }
      },
      error: () => {
        setImportMessage('Failed to parse CSV.');
        setImportSuccess(false);
      }
    });
  };

  // Remove a video by youtubeUrl, with undo
  const handleRemoveVideo = (youtubeUrl: string) => {
    if (!window.confirm('Are you sure you want to remove this video from your dashboard?')) return;
    const saved = localStorage.getItem('dashboardData');
    if (!saved) return;
    const current = JSON.parse(saved);
    const toDelete = current.find((v: any) => v.youtubeUrl === youtubeUrl);
    const updated = current.filter((v: any) => v.youtubeUrl !== youtubeUrl);
    localStorage.setItem('dashboardData', JSON.stringify(updated));
    setLastDeletedVideo(toDelete);
    setUndoTimer(10);
    if (undoTimeoutRef.current) clearTimeout(undoTimeoutRef.current);
    // Start countdown
    let seconds = 10;
    const tick = () => {
      seconds--;
      setUndoTimer(seconds);
      if (seconds > 0) {
        undoTimeoutRef.current = setTimeout(tick, 1000);
      } else {
        setLastDeletedVideo(null);
        setUndoTimer(0);
      }
    };
    undoTimeoutRef.current = setTimeout(tick, 1000);
  };

  // Undo delete
  const handleUndoDelete = () => {
    if (!lastDeletedVideo) return;
    const saved = localStorage.getItem('dashboardData');
    const current = saved ? JSON.parse(saved) : [];
    localStorage.setItem('dashboardData', JSON.stringify([lastDeletedVideo, ...current]));
    setLastDeletedVideo(null);
    setUndoTimer(0);
    if (undoTimeoutRef.current) clearTimeout(undoTimeoutRef.current);
    window.location.reload();
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[40vh]">
        <div className="text-white text-lg font-bold">No analysis data yet.<br />Please run an analysis.</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      {/* Page Header */}
      <div className="container mx-auto px-4 pt-8 pb-6">
        <h1 className="text-3xl font-bold text-white">Analytics</h1>
        <p className="text-white/70 text-sm mt-2">Analyze video performance and engagement metrics</p>
      </div>

      {/* Fixed Filters (Shown when scrolling) */}
      <div 
        className={`fixed top-4 left-1/2 -translate-x-1/2 w-[95%] max-w-5xl mx-auto card z-50 transition-all duration-300 ${isScrolledPastHero ? 'translate-y-0 opacity-100' : '-translate-y-20 opacity-0 pointer-events-none'}`}
      >
        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-5">
          {/* Search Bar */}
          <div className="flex-1 w-full lg:max-w-[400px]">
            <div className="relative">
              <input
                type="text"
                className="w-full text-lg font-medium input-field pl-5 pr-12 py-3"
                placeholder="Search by tag or keyword"
                value={searchKeyword}
                onChange={e => setSearchKeyword(e.target.value)}
              />
              {searchKeyword && (
                <button
                  className="absolute right-3 top-1/2 -translate-y-1/2 p-1.5 text-white/80 hover:text-white transition-colors"
                  onClick={() => setSearchKeyword('')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
          </div>
          {/* Filters Row */}
          <div className="flex-1 flex flex-wrap items-center gap-3 w-full">
            <select
              className="flex-1 min-w-[160px] text-base font-medium input-field px-4 py-3"
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
            >
              <option value="All">All Categories</option>
              <option value="Games">Games</option>
              <option value="Other">Other</option>
            </select>
            {selectedCategory !== 'All' && (
              <select
                className="flex-1 min-w-[180px] text-base font-medium input-field px-4 py-3"
                value={selectedChannel}
                onChange={e => setSelectedChannel(e.target.value)}
              >
                <option value="All Channels">All Channels</option>
                {channelNames.map(name => (
                  <option key={name} value={name}>{name}</option>
                ))}
              </select>
            )}
            <input 
              type="date" 
              className="flex-1 min-w-[180px] text-base font-medium input-field px-4 py-3"
              value={selectedDate} 
              onChange={e => setSelectedDate(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Fixed KPIs (Shown when scrolling) */}
      <div
        className={`fixed right-4 top-24 w-64 transition-all duration-300 ease-out z-20 ${isScrolledPastHero ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0 pointer-events-none'}`}
      >
        <div className="flex flex-col gap-4 p-2 max-h-[calc(100vh-8rem)] overflow-y-auto custom-scrollbar pr-1">
          {kpiFields.map((kpi, i) => (
            <div 
              key={i} 
              className="bg-white/10 backdrop-blur-lg rounded-xl p-4 shadow-xl border border-white/20 flex flex-col items-center transition-transform hover:scale-[1.02] hover:bg-white/15"
            >
              <div className="text-xl font-bold text-yellow-300 mb-1">
                {kpi.get((selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered)}
              </div>
              <div className="text-sm text-white/90 font-medium text-center">{kpi.title}</div>
            </div>
          ))}
        </div>
      </div>
      
      <style dangerouslySetInnerHTML={{
        __html: `
          .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 10px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        `
      }} />

      {/* Export/Import Controls */}
      <div className="flex flex-col sm:flex-row gap-6 mb-8 items-center justify-between bg-white/5 rounded-xl p-6 border border-white/10">
        <div className="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto">
          <button
            className="button-primary flex items-center gap-2 whitespace-nowrap"
            onClick={handleExportCSV}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" /></svg>
            Export Filtered Data (CSV)
          </button>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="file"
              accept=".csv"
              className="hidden"
              onChange={handleImportCSV}
            />
            <span className="button-secondary flex items-center gap-2 whitespace-nowrap">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M4 16v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-2M7 10l5 5 5-5M12 15V3" /></svg>
              Import Data (CSV)
            </span>
          </label>
        </div>
        <div className="text-sm text-white/60 text-center sm:text-right w-full sm:w-auto">
          Please upload data exported from this system to avoid conflicts or errors.
        </div>
      </div>
      {importMessage && (
        <div className={`mb-4 px-4 py-2 rounded text-center font-semibold ${importSuccess ? 'bg-green-600/80 text-white' : 'bg-red-600/80 text-white'}`}>{importMessage}</div>
      )}

      {/* Scroll Sentinel and Original Filters/KPIs (Hidden when scrolled past hero) */}
      <div ref={scrollSentinelRef} className={`flex flex-col lg:flex-row justify-between items-stretch gap-6 mb-8 transition-opacity duration-300 ${isScrolledPastHero ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>
        {/* Filters and Search - Original Position */}
        <div className="w-full lg:w-1/2 flex flex-col gap-3">
          {/* Search Bar - Original */}
          <div className="flex items-center gap-2">
            <input
              type="text"
              className="flex-1 input-field text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400 px-4 py-2 rounded-lg shadow min-w-0"
              style={{ backgroundColor: '#232323', color: '#fff' }}
              placeholder="Search by tag or keyword"
              value={searchKeyword}
              onChange={e => setSearchKeyword(e.target.value)}
            />
            {searchKeyword && (
              <button
                className="shrink-0 px-3 py-1 rounded bg-red-500/80 text-white font-bold text-xs shadow hover:bg-red-600 transition h-9"
                onClick={() => setSearchKeyword('')}
              >
                Clear
              </button>
            )}
          </div>
          {/* Filter Bar - Original */}
          <div className="flex flex-col xs:flex-row gap-2 items-stretch">
            <select
              className="input-field flex-1 min-w-[120px] text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400 px-3 py-2 text-sm"
              style={{ backgroundColor: '#232323', color: '#fff' }}
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
            >
              <option value="All">All Categories</option>
              <option value="Games" style={{ backgroundColor: '#232323', color: '#fff' }}>Games</option>
              <option value="Other" style={{ backgroundColor: '#232323', color: '#fff' }}>Other</option>
            </select>
            {selectedCategory !== 'All' && (
              <select
                className="input-field flex-1 min-w-[140px] text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400 px-3 py-2 text-sm"
                style={{ backgroundColor: '#232323', color: '#fff' }}
                value={selectedChannel}
                onChange={e => setSelectedChannel(e.target.value)}
              >
                <option value="All Channels">All Channels</option>
                {channelNames.map(name => (
                  <option key={name} value={name} style={{ backgroundColor: '#232323', color: '#fff' }}>{name}</option>
                ))}
              </select>
            )}
            <input 
              type="date" 
              className="input-field flex-1 min-w-[140px] text-white font-semibold bg-[#232323] border border-yellow-400 px-3 py-2 text-sm" 
              style={{ backgroundColor: '#232323', color: '#fff' }} 
              value={selectedDate} 
              onChange={e => setSelectedDate(e.target.value)} 
            />
            <select
              className="input-field flex-1 min-w-[140px] text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400 px-3 py-2 text-sm"
              style={{ backgroundColor: '#232323', color: '#fff' }}
              value={sortBy}
              onChange={e => setSortBy(e.target.value)}
            >
              <option value="views" style={{ backgroundColor: '#232323', color: '#fff' }}>Sort by Views</option>
              <option value="engagement" style={{ backgroundColor: '#232323', color: '#fff' }}>Sort by Engagement</option>
              <option value="recency" style={{ backgroundColor: '#232323', color: '#fff' }}>Sort by Recency</option>
            </select>
          </div>
        </div>
        {/* KPIs - Original Position */}
        <div className="w-full lg:w-1/2 flex flex-col gap-4">
          {(() => {
            const kpiSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
            return (
              <div className="grid grid-cols-2 gap-3">
                {kpiFields.map((kpi, i) => (
                  <div key={i} className="bg-white/10 backdrop-blur-lg rounded-xl p-4 shadow-xl border border-white/20 flex flex-col items-center justify-center">
                    <div className="text-xl font-bold text-yellow-300 mb-1">{kpi.get(kpiSource)}</div>
                    <div className="text-white/90 font-medium text-center text-sm">{kpi.title}</div>
                  </div>
                ))}
              </div>
            );
          })()}
        </div>
      </div>
      {/* Tabs */}
      <div className="flex gap-4 mb-6 border-b border-white/10">
        {tabs.map((tab, i) => (
          <button
            key={tab.label}
            onClick={() => setActiveTab(i)}
            className={`px-4 py-2 font-bold transition-colors duration-200 ${activeTab === i ? 'bg-gradient-to-r from-yellow-400 to-red-400 text-white rounded-t-lg shadow' : 'text-orange-200 hover:text-white'}`}
          >
            {tab.label}
          </button>
        ))}
      </div>
      {/* Tab Content */}
      <div className="bg-white/10 backdrop-blur-lg rounded-xl p-8 shadow-xl border border-white/20">
        {activeTab === 0 && (
          <>
            {/* Video Patterns Tab */}
            <h3 className="text-xl font-bold mb-4 text-white">Trending Video Patterns</h3>
            {/* Calculate tag and pattern data based on selected channel */}
            {(() => {
              // Use channelFiltered if a specific channel is selected, otherwise filtered
              const patternSource = (selectedChannel && selectedChannel !== 'All Channels') ? channelFiltered : filtered;
              // Tag counts
              const tagCounts: Record<string, number> = {};
              patternSource.forEach(v => {
                let tags: string[] = [];
                if (Array.isArray(v.tags)) tags = v.tags;
                else if (typeof v.tags === 'string') {
                  try {
                    const parsed = JSON.parse(v.tags);
                    if (Array.isArray(parsed)) tags = parsed;
                    else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                  } catch {
                    tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                  }
                }
                tags.forEach((tag: string) => {
                  tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                });
              });
              const topTags = Object.entries(tagCounts).sort((a, b) => b[1] - a[1]).slice(0, 5);
              const rechartsTagData = topTags.map(([tag, count]) => ({ tag, count }));
              // Pattern counts
              const patternCounts: Record<string, number> = {};
              patternSource.forEach(v => {
                const sources = [v.title, v.description];
                sources.forEach(text => {
                  if (typeof text === 'string' && text.trim().length > 0) {
                    // Get first 2-3 words
                    const match = text.trim().match(/^([\w"']+\s+){1,2}[\w"']+/);
                    if (match) {
                      const phrase = match[0].trim();
                      patternCounts[phrase] = (patternCounts[phrase] || 0) + 1;
                    }
                  }
                });
              });
              const topPatterns = Object.entries(patternCounts).sort((a, b) => b[1] - a[1]).slice(0, 5);
              return (
                <>
                  <div className="mb-8">
                    <div className="text-white/80 mb-2">Most Common Keywords</div>
                    {rechartsTagData.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-12 text-white/70">
                        <svg width="48" height="48" fill="none" viewBox="0 0 24 24" className="mb-2 text-yellow-300"><path fill="currentColor" d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20Zm1 15h-2v-2h2v2Zm0-4h-2V7h2v6Z"/></svg>
                        <div className="text-lg font-semibold">No keyword data available for the current filter.</div>
                        <div className="text-sm text-white/50 mt-1">Try changing your filters or running a new analysis.</div>
                      </div>
                    ) : (
                      <ResponsiveContainer width="100%" height={220}>
                        <BarChart data={rechartsTagData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                          <XAxis
                            dataKey="tag"
                            stroke="#fff"
                            fontSize={15}
                            tickLine={false}
                            axisLine={false}
                            interval={0}
                            angle={0}
                            textAnchor="middle"
                            style={{ fontWeight: 'bold' }}
                            height={40}
                            tick={({ x, y, payload }) => (
                              <text
                                x={x}
                                y={y + 16}
                                fill="#fff"
                                fontWeight="bold"
                                fontSize="15"
                                textAnchor="middle"
                              >
                                {String(payload.value).length > 12 ? String(payload.value).slice(0, 12) + '…' : payload.value}
                              </text>
                            )}
                          />
                          <YAxis stroke="#fff" fontSize={13} tickLine={false} axisLine={false} allowDecimals={false} />
                          <Tooltip cursor={{ fill: '#fff3' }} contentStyle={{ background: '#222', border: 'none', color: '#fff' }} />
                          <Bar dataKey="count" fill="url(#barGradient)" barSize={24} radius={[8, 8, 0, 0]}>
                            {rechartsTagData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill="url(#barGradient)" />
                            ))}
                          </Bar>
                          <defs>
                            <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor="#facc15" />
                              <stop offset="100%" stopColor="#eab308" />
                            </linearGradient>
                          </defs>
                        </BarChart>
                      </ResponsiveContainer>
                    )}
                  </div>
                  <div>
                    <div className="text-white/80 mb-2">Title/Description Patterns</div>
                    <ul className="list-disc pl-6 text-white/90">
                      {topPatterns.length === 0 ? (
                        <li className="text-white/70 mt-4">No title/description patterns found for the current filter.</li>
                      ) : (
                        topPatterns.map(([pattern, count], i) => (
                          <li key={i} className="mb-1">
                            <span className="font-bold">{pattern}</span> <span className="text-yellow-300 font-bold">({count})</span>
                          </li>
                        ))
                      )}
                    </ul>
                  </div>
                </>
              );
            })()}
          </>
        )}
        {activeTab === 1 && (
          <>
            {/* Content Formats Tab */}
            <h3 className="text-xl font-bold mb-4 text-white">Content Formats</h3>
            <div className="mb-10">
              <div className="text-white/80 mb-4">Video Length Distribution</div>
              <div className="flex items-end gap-10 justify-center">
                {pieData.map((slice, i) => (
                  <div key={slice.label} className={`relative flex flex-col items-center justify-center w-36 h-36`}> 
                    <div className={`absolute inset-0 rounded-full bg-gradient-to-br ${slice.color} opacity-80 shadow-xl border-4 border-white/10`} />
                    <div className="relative z-10 flex flex-col items-center justify-center w-32 h-32 rounded-full bg-white/10 shadow-lg border-2 border-white/20">
                      <span className="text-2xl font-extrabold text-white drop-shadow mb-1">{slice.value}</span>
                      <span className="text-lg font-bold text-white/90 mb-1">{slice.label}</span>
                      <span className="text-yellow-200 font-bold text-base">{totalVideos ? Math.round((slice.value/totalVideos)*100) : 0}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {/* --- ADVANCED CONTENT FORMAT ANALYSIS --- */}
            {(() => {
              // Map dashboard data to analysis shape
              const analysisData: AnalysisVideoData[] = contentSource.map(v => ({
                id: v.youtubeUrl || v.id || v.title || Math.random().toString(),
                title: v.title || '',
                duration: typeof v.duration === 'string' ? Number(v.duration) : v.duration,
                views: Number(v.views) || 0,
                likes: Number(v.likes) || 0,
                comments: Number(v.comments) || 0,
                // Always calculate engagement rate as decimal
                engagementRate: (Number(v.likes) + Number(v.comments)) / (Number(v.views) || 1),
                likeRate: Number(v.likes) / (Number(v.views) || 1),
                viewsPerHour: Number(v.viewsPerHour) || 0,
                publishedAt: v.publishedDate || v.publishedAt || '',
              })).filter(v => !isNaN(v.duration) && v.duration > 0 && v.views > 0);
              if (!analysisData.length) {
                return (
                  <div className="text-center text-white/60 italic mb-8">
                    No videos match the current filters.
                  </div>
                );
              }
              // Example industry benchmarks (could be dynamic)
              const industryBenchmarks = {
                Shorts: { engagementRate: 0.045 },
                'Short-form': { engagementRate: 0.035 },
                'Mid-form': { engagementRate: 0.03 },
                'Long-form': { engagementRate: 0.025 },
              };
              const result: AnalysisResult = analyzeContentPerformance(analysisData, industryBenchmarks);
              // Table: Metrics by format
              const formatLabels: Record<ContentFormat, string> = {
                Shorts: 'Shorts (≤1m)',
                'Short-form': 'Short-form (1–5m)',
                'Mid-form': 'Mid-form (5–15m)',
                'Long-form': 'Long-form (>15m)',
              };
              // Chart data: view velocity vs. duration
              const chartData = analysisData.map(v => ({
                title: v.title,
                duration: v.duration / 60, // minutes
                viewsPerHour: v.viewsPerHour,
                engagementRate: Math.min(1, v.engagementRate) * 100,
                format: formatLabels[(v.duration <= 60 ? 'Shorts' : v.duration <= 300 ? 'Short-form' : v.duration <= 900 ? 'Mid-form' : 'Long-form') as ContentFormat],
              }));
              // Trend summary: YoY or MoM
              const now = new Date();
              const lastYear = new Date(now.getFullYear() - 1, now.getMonth());
              const formatTrends = result.trends;
              function getYoYChange(format: ContentFormat) {
                const months = Object.keys(formatTrends[format]).sort();
                if (months.length < 13) return null;
                const thisMonth = months[months.length - 1];
                const lastYearMonth = months.find(m => m === `${lastYear.getFullYear()}-${String(lastYear.getMonth() + 1).padStart(2, '0')}`);
                if (!lastYearMonth) return null;
                const nowVal = formatTrends[format][thisMonth]?.avgEngagementRate;
                const lastVal = formatTrends[format][lastYearMonth]?.avgEngagementRate;
                if (!nowVal || !lastVal) return null;
                return ((nowVal - lastVal) / lastVal) * 100;
              }
              return (
                <div className="mb-10">
                  {/* Table: Metrics by Format */}
                  <div className="mb-8 overflow-x-auto">
                    <table className="min-w-full bg-white/10 rounded-xl overflow-hidden shadow border border-yellow-400/20">
                      <thead>
                        <tr>
                          <th className="px-4 py-2 text-left text-yellow-300 font-bold">Format</th>
                          <th className="px-4 py-2 text-left text-yellow-200">Videos</th>
                          <th className="px-4 py-2 text-left text-yellow-200">Avg. Views/Hour</th>
                          <th className="px-4 py-2 text-left text-yellow-200">Engagement Rate</th>
                          <th className="px-4 py-2 text-left text-yellow-200">Like Rate</th>
                          <th className="px-4 py-2 text-left text-yellow-200">Comment Rate</th>
                        </tr>
                      </thead>
                      <tbody>
                        {(Object.keys(result.byFormat) as ContentFormat[]).map(format => (
                          <tr key={format} className="border-t border-yellow-400/10">
                            <td className="px-4 py-2 font-bold text-white">{formatLabels[format]}</td>
                            <td className="px-4 py-2 text-white/90">{result.byFormat[format].count}</td>
                            <td className="px-4 py-2 text-white/90">{result.byFormat[format].avgViewsPerHour.toFixed(1)}</td>
                            <td className="px-4 py-2 text-white/90">{(result.byFormat[format].avgEngagementRate * 100).toFixed(2)}%</td>
                            <td className="px-4 py-2 text-white/90">{(result.byFormat[format].avgLikeRate * 100).toFixed(2)}%</td>
                            <td className="px-4 py-2 text-white/90">{(result.byFormat[format].avgCommentRate * 100).toFixed(2)}%</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  {/* Chart: View Velocity vs. Duration */}
                  <div className="mb-8">
                    <div className="text-white/80 mb-2 font-semibold">View Velocity vs. Duration</div>
                    <div className="flex justify-between items-center mb-2 px-2">
                      <span className="text-white/80 font-semibold">Views/Hour (left)</span>
                      <span className="text-indigo-300 font-semibold">Engagement % (right)</span>
                    </div>
                    <ResponsiveContainer width="100%" height={320}>
                      <BarChart data={chartData} margin={{ top: 16, right: 32, left: 48, bottom: 16 }}>
                        <CartesianGrid stroke="#fff" strokeOpacity={0.08} strokeDasharray="2 4" />
                        <XAxis 
                          dataKey="duration" 
                          type="number" 
                          domain={[0, Math.max(1, Math.ceil(Math.max(...chartData.map(d => d.duration || 0))))]} 
                          tickFormatter={d => `${Math.round(d)}m`} 
                          allowDecimals={false}
                          tick={{ fill: '#fff', fontWeight: 600 }}
                        />
                        <YAxis 
                          yAxisId="left" 
                          orientation="left" 
                          stroke="#facc15" 
                          tick={{ fill: '#fff', fontWeight: 600 }}
                          axisLine={{ stroke: '#facc15', strokeWidth: 1 }}
                        />
                        <YAxis 
                          yAxisId="right" 
                          orientation="right" 
                          stroke="#6366f1" 
                          tick={{ fill: '#fff', fontWeight: 600 }}
                          axisLine={{ stroke: '#6366f1', strokeWidth: 1 }}
                          domain={[0, 100]}
                        />
                        <Tooltip
                          labelFormatter={(label: any, payload: any[]) => payload?.[0]?.payload?.title ?? label}
                          formatter={(value: any, name: string) => {
                            // Keep numeric formatting only for Views/Hour; hide other numeric series names
                            if (name === 'Views/Hour') {
                              return [
                                typeof value === 'number'
                                  ? Number(value).toLocaleString(undefined, { maximumFractionDigits: 2 })
                                  : value,
                                name,
                              ];
                            }
                            // Hide engagement % line from tooltip (since title shown as label)
                            return ['', ''];
                          }}
                          contentStyle={{ background: '#232323', border: '1px solid #facc15', color: '#fff' }} />
                        
                        <Bar yAxisId="left" dataKey="viewsPerHour" fill="#facc15" name="Views/Hour" barSize={30} minPointSize={10} />
                        <Line 
                          yAxisId="right" 
                          type="monotone" 
                          dataKey="engagementRate"
                          stroke="#6366f1" 
                          strokeWidth={3} 
                          dot={{ r: 5, fill: '#6366f1', stroke: '#fff', strokeWidth: 1 }} 
                          name="Engagement %" 
                          isAnimationActive={false}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                    <div className="text-center text-white/80 font-semibold mt-2">Duration (min)</div>
                  </div>
                  {/* Recommendations */}
                  <div className="mb-8">
                    <div className="text-white/80 mb-2 font-semibold">Actionable Recommendations</div>
                    <ul className="list-disc pl-6 text-yellow-200 font-semibold space-y-2">
                      {result.recommendations.length ? result.recommendations.map((rec, i) => (
                        <li key={i}>{rec}</li>
                      )) : <li>No specific recommendations. Content is performing near benchmarks.</li>}
                    </ul>
                  </div>
                  {/* Historical Trend Summary */}
                  <div className="mb-8">
                    <div className="text-white/80 mb-2 font-semibold">Historical Trend Summary</div>
                    <ul className="list-disc pl-6 text-white/90 space-y-1">
                      {(Object.keys(result.trends) as ContentFormat[]).map(format => {
                        const change = getYoYChange(format);
                        if (change === null) return null;
                        return (
                          <li key={format}>
                            {formatLabels[format]} engagement {change > 0 ? 'increased' : 'decreased'} {Math.abs(change).toFixed(1)}% YoY.
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              );
            })()}
            {/* Video Duration Insights */}
            {(() => {
              // Gather durations
              const durations = contentSource.map(v => {
                let d = v.duration;
                if (typeof d === 'string') d = Number(d);
                return typeof d === 'number' && !isNaN(d) ? d : null;
              }).filter((d): d is number => d !== null);
              if (durations.length === 0) return null;
              // Average
              const avg = durations.reduce((a, b) => a + b, 0) / durations.length;
              // Median
              const sorted = [...durations].sort((a, b) => a - b);
              const median = sorted.length % 2 === 1 ? sorted[(sorted.length-1)/2] : (sorted[sorted.length/2-1] + sorted[sorted.length/2]) / 2;
              // Range
              const min = Math.min(...durations);
              const max = Math.max(...durations);
              // Format seconds to mm:ss with 1 decimal for seconds
              const fmt = (s: number) => `${Math.floor(s/60)}:${(s%60).toFixed(1).padStart(4,'0')}`;
              // Correlate duration with engagement
              const getAvgEngagement = (filter: (d: number) => boolean) => {
                const vids = contentSource.filter(v => {
                  let d = v.duration;
                  if (typeof d === 'string') d = Number(d);
                  return typeof d === 'number' && !isNaN(d) && filter(d);
                });
                if (!vids.length) return 'N/A';
                const avgEng = vids.reduce((sum, v) => sum + Number(v.engagementRate || 0), 0) / vids.length;
                return avgEng.toFixed(2) + '%';
              };
              return (
                <div className="mb-10 bg-gradient-to-br from-yellow-900/30 to-orange-900/30 rounded-xl p-6 border border-yellow-400/20 shadow-lg flex flex-col items-center justify-center">
                  <h4 className="text-xl font-extrabold text-yellow-300 mb-4 flex items-center gap-2 justify-center">
                    <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                      <path stroke="currentColor" strokeWidth="2" strokeLinecap="round" d="M12 6v6l4 2" />
                    </svg>
                    Video Duration Insights
                  </h4>
                  <div className="flex flex-wrap gap-6 mb-4 justify-center">
                    <div className="flex flex-col items-center bg-white/10 rounded-lg px-6 py-3 shadow border border-yellow-300/20 min-w-[120px]">
                      <span className="text-lg font-bold text-yellow-200 flex items-center gap-1">
                        <svg className="w-5 h-5 text-yellow-300" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                          <path stroke="currentColor" strokeWidth="2" strokeLinecap="round" d="M12 6v6l4 2" />
                        </svg>
                        Average
                      </span>
                      <span className="text-2xl font-extrabold text-white">{avg.toFixed(1)}s</span>
                      <span className="text-yellow-200 text-sm">({fmt(avg)})</span>
                    </div>
                    <div className="flex flex-col items-center bg-white/10 rounded-lg px-6 py-3 shadow border border-yellow-300/20 min-w-[120px]">
                      <span className="text-lg font-bold text-yellow-200 flex items-center gap-1">
                        <svg className="w-5 h-5 text-yellow-300" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <rect x="4" y="11" width="16" height="2" rx="1" fill="currentColor" />
                        </svg>
                        Median
                      </span>
                      <span className="text-2xl font-extrabold text-white">{median.toFixed(1)}s</span>
                      <span className="text-yellow-200 text-sm">({fmt(median)})</span>
                    </div>
                    <div className="flex flex-col items-center bg-white/10 rounded-lg px-6 py-3 shadow border border-yellow-300/20 min-w-[120px]">
                      <span className="text-lg font-bold text-yellow-200 flex items-center gap-1">
                        <svg className="w-5 h-5 text-yellow-300" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <path stroke="currentColor" strokeWidth="2" strokeLinecap="round" d="M4 20l16-16" />
                        </svg>
                        Range
                      </span>
                      <span className="text-2xl font-extrabold text-white">{min}s - {max}s</span>
                      <span className="text-yellow-200 text-sm">({fmt(min)} - {fmt(max)})</span>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-6 mt-2 justify-center">
                    <div className="flex flex-col items-center bg-yellow-400/10 rounded-lg px-4 py-2 shadow border border-yellow-300/20 min-w-[140px]">
                      <span className="font-bold text-yellow-200">Short Avg. Engagement</span>
                      <span className="text-lg font-extrabold text-yellow-300">{getAvgEngagement(d => d < 240)}</span>
                    </div>
                    <div className="flex flex-col items-center bg-yellow-400/10 rounded-lg px-4 py-2 shadow border border-yellow-300/20 min-w-[140px]">
                      <span className="font-bold text-yellow-200">Medium Avg. Engagement</span>
                      <span className="text-lg font-extrabold text-yellow-300">{getAvgEngagement(d => d >= 240 && d <= 1200)}</span>
                    </div>
                    <div className="flex flex-col items-center bg-yellow-400/10 rounded-lg px-4 py-2 shadow border border-yellow-300/20 min-w-[140px]">
                      <span className="font-bold text-yellow-200">Long Avg. Engagement</span>
                      <span className="text-lg font-extrabold text-yellow-300">{getAvgEngagement(d => d > 1200)}</span>
                    </div>
                  </div>
                  {/* Correlation summary */}
                  {(() => {
                    // Get numeric values for each bucket
                    const short = getAvgEngagement(d => d < 240);
                    const medium = getAvgEngagement(d => d >= 240 && d <= 1200);
                    const long = getAvgEngagement(d => d > 1200);
                    // Parse to numbers, ignore N/A
                    const vals = [
                      { label: 'Short', value: parseFloat(short) },
                      { label: 'Medium', value: parseFloat(medium) },
                      { label: 'Long', value: parseFloat(long) }
                    ].filter(x => !isNaN(x.value));
                    if (vals.length < 2) return null;
                    const best = vals.reduce((a, b) => (a.value > b.value ? a : b));
                    return (
                      <div className="mt-6 text-center text-lg font-bold text-yellow-200 bg-yellow-900/30 rounded p-3 shadow-inner max-w-xl mx-auto">
                        {best.label} videos get higher engagement on average.
                      </div>
                    );
                  })()}
                </div>
              );
            })()}
            {/* Engagement Funnel */}
            {(() => {
              if (!contentSource.length) return null;
              // Aggregate totals
              const totalViews = contentSource.reduce((sum, v) => sum + (Number(v.views) || 0), 0);
              const totalLikes = contentSource.reduce((sum, v) => sum + (Number(v.likes) || 0), 0);
              const totalComments = contentSource.reduce((sum, v) => sum + (Number(v.comments) || 0), 0);
              if (totalViews === 0) {
                return (
                  <div className="mb-10 bg-white/5 rounded-xl p-6 border border-white/10 text-center text-white/70">
                    Not enough data to show the engagement funnel.
                  </div>
                );
              }
              const likeRate = totalLikes / totalViews;
              const commentRate = totalComments / totalViews;
              // Funnel steps
              const steps = [
                { label: 'Views', value: totalViews, color: 'from-yellow-400 to-yellow-500' },
                { label: 'Likes', value: totalLikes, color: 'from-orange-400 to-orange-500' },
                { label: 'Comments', value: totalComments, color: 'from-red-400 to-red-500' },
              ];
              // Find max for bar width
              const maxVal = Math.max(...steps.map(s => s.value));
              return (
                <div className="mb-10 bg-gradient-to-br from-yellow-900/30 to-orange-900/30 rounded-xl p-6 border border-yellow-400/20 shadow-lg flex flex-col items-center justify-center">
                  <h4 className="text-xl font-extrabold text-yellow-300 mb-4 flex items-center gap-2 justify-center">
                    <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path stroke="currentColor" strokeWidth="2" strokeLinecap="round" d="M12 4v16m8-8H4" />
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                    </svg>
                    Engagement Funnel
                  </h4>
                  <div className="w-full max-w-lg flex flex-col gap-4 mb-4">
                    {steps.map((step, i) => (
                      <div key={step.label} className="flex items-center gap-4">
                        <div className="w-24 text-right text-white/90 font-bold">{step.label}</div>
                        <div className={`flex-1 h-8 rounded-full bg-gradient-to-r ${step.color} shadow-lg border border-white/10 relative`} style={{ width: `${(step.value / maxVal) * 100}%`, minWidth: 40 }}>
                          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-white font-extrabold text-lg drop-shadow">{step.value.toLocaleString()}</span>
                        </div>
                        {i > 0 && (
                          <div className="w-28 text-yellow-200 text-sm font-bold text-center">
                            {steps[i-1].value > 0 ? `${((step.value/steps[i-1].value)*100).toFixed(1)}%` : '--'}
                            <span className="ml-1 text-white/60">({step.label}/{steps[i-1].label})</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  <div className="text-yellow-200 font-bold text-center">
                    Like Rate: {(likeRate*100).toFixed(2)}% &nbsp;|&nbsp; Comment Rate: {(commentRate*100).toFixed(2)}%
                  </div>
                </div>
              );
            })()}
            {/* Content Type/Format Breakdown */}
            {(() => {
              if (!contentSource.length) return null;
              // Helper to infer type
              const inferType = (v: any) => {
                const title = (v.title || '').toLowerCase();
                const desc = (v.description || '').toLowerCase();
                let tags: string[] = [];
                if (Array.isArray(v.tags)) tags = v.tags.map((t: string) => t.toLowerCase());
                else if (typeof v.tags === 'string') {
                  try {
                    const parsed = JSON.parse(v.tags);
                    if (Array.isArray(parsed)) tags = parsed.map((t: string) => t.toLowerCase());
                    else tags = String(v.tags).split(',').map((t: string) => t.trim().toLowerCase()).filter(Boolean);
                  } catch {
                    tags = String(v.tags).split(',').map((t: string) => t.trim().toLowerCase()).filter(Boolean);
                  }
                }
                // Shorts: YouTube URL contains 'shorts' or duration < 60s
                if ((v.youtubeUrl && v.youtubeUrl.includes('shorts')) || (v.duration && Number(v.duration) < 60)) return 'Shorts';
                if (tags.includes('shorts')) return 'Shorts';
                if (title.includes('tutorial') || desc.includes('tutorial') || tags.includes('tutorial') || title.includes('how to') || desc.includes('how to')) return 'Tutorial';
                if (title.includes('challenge') || desc.includes('challenge') || tags.includes('challenge')) return 'Challenge';
                if (title.includes('review') || desc.includes('review') || tags.includes('review')) return 'Review';
                if (title.includes('gameplay') || desc.includes('gameplay') || tags.includes('gameplay')) return 'Gameplay';
                if (title.includes('vlog') || desc.includes('vlog') || tags.includes('vlog')) return 'Vlog';
                return 'Other';
              };
              // Aggregate by type
              const typeMap: Record<string, any[]> = {};
              contentSource.forEach(v => {
                const type = inferType(v);
                if (!typeMap[type]) typeMap[type] = [];
                typeMap[type].push(v);
              });
              const typeColors: Record<string, string> = {
                Shorts: 'bg-yellow-400/30 text-yellow-300',
                Tutorial: 'bg-blue-400/30 text-blue-300',
                Challenge: 'bg-red-400/30 text-red-300',
                Review: 'bg-green-400/30 text-green-300',
                Gameplay: 'bg-orange-400/30 text-orange-300',
                Vlog: 'bg-pink-400/30 text-pink-300',
                Other: 'bg-white/10 text-white/80',
              };
              // Helper to get top tags for a group of videos
              const getTopTags = (vids: any[], max = 5) => {
                const tagCounts: Record<string, number> = {};
                vids.forEach(v => {
                  let tags: string[] = [];
                  if (Array.isArray(v.tags)) tags = v.tags;
                  else if (typeof v.tags === 'string') {
                    try {
                      const parsed = JSON.parse(v.tags);
                      if (Array.isArray(parsed)) tags = parsed;
                      else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                    } catch {
                      tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                    }
                  }
                  tags.forEach((tag: string) => {
                    if (!tag) return;
                    tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                  });
                });
                return Object.entries(tagCounts)
                  .sort((a, b) => b[1] - a[1])
                  .slice(0, max)
                  .map(([tag]) => tag);
              };
              // Hybrid outlier detection for each type
              const rows = Object.entries(typeMap).map(([type, vids]) => {
                const viewsArr = vids.map(v => Number(v.views) || 0).filter(n => n > 0);
                const avgViews = viewsArr.length ? Math.round(viewsArr.reduce((a, b) => a + b, 0) / viewsArr.length) : 0;
                const avgEng = vids.length ? (vids.reduce((a, v) => a + (Number(v.engagementRate) || 0), 0) / vids.length).toFixed(2) : '0.00';
                const topTags = getTopTags(vids, 8);
                // Top 1-2 videos by views
                const topVideos = [...vids].sort((a, b) => Number(b.views || 0) - Number(a.views || 0)).slice(0, 2);
                return { type, count: vids.length, avgViews, avgEng, topTags, topVideos };
              }).sort((a, b) => b.count - a.count);
              if (!rows.length) return null;
              return (
                <div className="mb-10 bg-gradient-to-br from-yellow-900/30 to-orange-900/30 rounded-xl p-6 border border-yellow-400/20 shadow-lg flex flex-col items-center justify-center">
                  <h4 className="text-xl font-extrabold text-yellow-300 mb-4 flex items-center gap-2 justify-center">
                    <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <rect x="4" y="4" width="16" height="16" rx="4" fill="#facc15" stroke="#eab308" strokeWidth="2" />
                      <path stroke="#eab308" strokeWidth="2" d="M8 8h8v8H8z" />
                    </svg>
                    Content Type/Format Breakdown
                  </h4>
                  <div className="w-full max-w-2xl overflow-x-auto">
                    <table className="w-full text-left rounded-xl overflow-hidden">
                      <thead>
                        <tr className="bg-yellow-400/10 text-yellow-200">
                          <th className="py-2 px-4 font-bold">Type</th>
                          <th className="py-2 px-4 font-bold"># Videos</th>
                          <th className="py-2 px-4 font-bold">Avg. Views</th>
                          <th className="py-2 px-4 font-bold">Avg. Engagement</th>
                          <th className="py-2 px-4 font-bold">Top Tags</th>
                          <th className="py-2 px-4 font-bold">Top Videos</th>
                        </tr>
                      </thead>
                      <tbody>
                        {rows.map(row => (
                          <tr key={row.type} className={`border-b border-white/10 ${typeColors[row.type] || ''}`}>
                            <td className="py-2 px-4 font-bold whitespace-nowrap">{row.type}</td>
                            <td className="py-2 px-4">{row.count}</td>
                            <td className="py-2 px-4">{row.avgViews.toLocaleString()}</td>
                            <td className="py-2 px-4">{row.avgEng}%</td>
                            <td className="py-2 px-4">
                              <div className="flex gap-2 overflow-x-auto max-w-[180px] tag-scroll">
                                {row.topTags.length === 0 ? <span className="text-white/40 italic">None</span> :
                                  row.topTags.map((tag: string, i: number) => (
                                    <span key={i} className="px-2 py-1 rounded-full bg-yellow-400/20 text-yellow-300 text-xs font-bold shadow border border-yellow-300/30 whitespace-nowrap">{tag}</span>
                                  ))}
                              </div>
                            </td>
                            <td className="py-2 px-4">
                              <div className="flex gap-2 overflow-x-auto max-w-[220px] tag-scroll">
                                {row.topVideos && row.topVideos.length > 0 ? row.topVideos.map((v: any, idx: number) => {
                                  const multiplier = row.avgViews > 0 ? Number(v.views) / row.avgViews : 0;
                                  let emoji = '🏆';
                                  if (multiplier >= 3) emoji = '🔥';
                                  else if (multiplier >= 2) emoji = '✨';
                                  // Truncate title if too long
                                  const title = (v.title || v.channelName || v.channel || 'Untitled');
                                  const shortTitle = title.length > 18 ? title.slice(0, 16) + '…' : title;
                                  return (
                                    <span key={v.youtubeUrl || idx} className="flex items-center gap-1 px-2 py-1 rounded-full bg-white/10 text-yellow-200 text-xs font-bold shadow border border-yellow-300/30 whitespace-nowrap">
                                      <span className="font-bold text-base">{idx + 1}</span>
                                      <span>{emoji}</span>
                                      <span title={title}>{shortTitle}</span>
                                      <span className="text-yellow-400 font-bold">{multiplier.toFixed(1)}x</span>
                                    </span>
                                  );
                                }) : <span className="text-white/40 italic">None</span>}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              );
            })()}
            {/* Best Time to Publish */}
            {(() => {
              // Gather publishedAt and views for each video
              const timeData = contentSource
                .map(v => {
                  const date = v.publishedAt || v.publishedDate;
                  const views = Number(v.views);
                  if (!date || isNaN(views)) return null;
                  const d = new Date(date);
                  if (isNaN(d.getTime())) return null;
                  // Get day and hour in UTC
                  const day = d.getUTCDay(); // 0=Sun, 1=Mon, ...
                  const hour = d.getUTCHours();
                  return { day, hour, views };
                })
                .filter((v): v is { day: number; hour: number; views: number } => v !== null);
              if (timeData.length < 5) {
                return (
                  <div className="mb-10 bg-white/5 rounded-xl p-6 border border-white/10 text-center text-white/70">
                    Not enough data to determine the best time to publish. Try analyzing more videos!
                  </div>
                );
              }
              // Aggregate by day of week
              const dayNames = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'];
              const dayViews: Record<string, number[]> = {};
              dayNames.forEach(day => (dayViews[day] = []));
              timeData.forEach(({ day, views }) => {
                dayViews[dayNames[day]].push(views);
              });
              const avgDayViews = dayNames.map(day => ({
                day,
                avgViews: dayViews[day].length ? dayViews[day].reduce((a, b) => a + b, 0) / dayViews[day].length : 0
              }));
              // Find best day
              const bestDay = avgDayViews.reduce((a, b) => (a.avgViews > b.avgViews ? a : b));
              // Aggregate by hour (0-23)
              const hourViews: Record<number, number[]> = {};
              for (let i = 0; i < 24; i++) hourViews[i] = [];
              timeData.forEach(({ hour, views }) => {
                hourViews[hour].push(views);
              });
              const avgHourViews = Array.from({ length: 24 }, (_, hour) => ({
                hour,
                avgViews: hourViews[hour].length ? hourViews[hour].reduce((a, b) => a + b, 0) / hourViews[hour].length : 0
              }));
              // Find best hour
              const bestHour = avgHourViews.reduce((a, b) => (a.avgViews > b.avgViews ? a : b));
              return (
                <div className="mb-10 bg-gradient-to-br from-yellow-900/30 to-orange-900/30 rounded-xl p-6 border border-yellow-400/20 shadow-lg flex flex-col items-center justify-center">
                  <h4 className="text-xl font-extrabold text-yellow-300 mb-4 flex items-center gap-2 justify-center">
                    <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path stroke="currentColor" strokeWidth="2" strokeLinecap="round" d="M12 6v6l4 2" />
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                    </svg>
                    Best Time to Publish
                  </h4>
                  <div className="w-full max-w-2xl mb-6">
                    <ResponsiveContainer width="100%" height={180}>
                      <BarChart data={avgDayViews} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <XAxis dataKey="day" stroke="#fff" fontSize={15} tickLine={false} axisLine={false} />
                        <YAxis stroke="#fff" fontSize={13} tickLine={false} axisLine={false} allowDecimals={false} />
                        <Tooltip cursor={{ fill: '#fff3' }} contentStyle={{ background: '#222', border: 'none', color: '#fff' }} />
                        <Bar dataKey="avgViews" fill="url(#barDayGradient)" barSize={28} radius={[8, 8, 0, 0]}>
                          {avgDayViews.map((entry, index) => (
                            <Cell key={`cell-day-${index}`} fill={entry.day === bestDay.day ? '#facc15' : 'url(#barDayGradient)'} />
                          ))}
                        </Bar>
                        <defs>
                          <linearGradient id="barDayGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#facc15" />
                            <stop offset="100%" stopColor="#eab308" />
                          </linearGradient>
                        </defs>
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="w-full max-w-2xl mb-2">
                    <ResponsiveContainer width="100%" height={120}>
                      <BarChart data={avgHourViews} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <XAxis dataKey="hour" stroke="#fff" fontSize={13} tickLine={false} axisLine={false} />
                        <YAxis stroke="#fff" fontSize={13} tickLine={false} axisLine={false} allowDecimals={false} />
                        <Tooltip cursor={{ fill: '#fff3' }} contentStyle={{ background: '#222', border: 'none', color: '#fff' }} />
                        <Bar dataKey="avgViews" fill="url(#barHourGradient)" barSize={14} radius={[8, 8, 0, 0]}>
                          {avgHourViews.map((entry, index) => (
                            <Cell key={`cell-hour-${index}`} fill={entry.hour === bestHour.hour ? '#facc15' : 'url(#barHourGradient)'} />
                          ))}
                        </Bar>
                        <defs>
                          <linearGradient id="barHourGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#facc15" />
                            <stop offset="100%" stopColor="#eab308" />
                          </linearGradient>
                        </defs>
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="mt-2 text-center text-lg font-bold text-yellow-200 bg-yellow-900/30 rounded p-3 shadow-inner max-w-xl mx-auto">
                    {`Best day: ${bestDay.day} (${Math.round(bestDay.avgViews).toLocaleString()} avg views)`}<br />
                    {`Best hour: ${bestHour.hour}:00 (${Math.round(bestHour.avgViews).toLocaleString()} avg views)`}
                  </div>
                </div>
              );
            })()}
            <div className="mt-8">
              <h4 className="text-lg font-semibold text-white mb-4">Upload Days</h4>
              <div className="flex gap-4 justify-center">
                {weekDays.map(day => (
                  <div
                    key={day}
                    className="flex-1 min-w-[80px] bg-gradient-to-br from-yellow-400/70 to-orange-400/70 rounded-lg p-4 text-center text-white/90 font-bold shadow-md border border-white/10 transition-transform duration-200 hover:scale-105 hover:shadow-xl"
                    style={{ maxWidth: 120 }}
                  >
                    <div className="text-base mb-1">{day}</div>
                    <div className="text-2xl font-extrabold text-white drop-shadow">{dayCounts[day]}</div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
        {activeTab === 2 && (
          <>
            {/* Top Performers Tab */}
            <h3 className="text-xl font-bold mb-4 text-white">Top Performers</h3>
            {/* Filter for format */}
            <div className="mb-4 flex gap-4 items-center">
              <label className="text-white/80 font-semibold">Filter by Format:</label>
              <select
                className="input-field w-48 text-white font-semibold bg-[#232323] border border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400"
                style={{ backgroundColor: '#232323', color: '#fff' }}
                value={topFormatFilter || 'All'}
                onChange={e => setTopFormatFilter(e.target.value as ContentFormat | 'All')}
              >
                <option value="All">All</option>
                <option value="Shorts">Shorts</option>
                <option value="Short-form">Short-form</option>
                <option value="Mid-form">Mid-form</option>
                <option value="Long-form">Long-form</option>
              </select>
            </div>
            {/* Outlier Banner for Top Performers */}
            {(() => {
              // Add filter logic for top performers
              const filteredTop = (topFormatFilter && topFormatFilter !== 'All')
                ? contentSource.filter(v => getContentFormat(Number(v.duration)) === topFormatFilter)
                : contentSource;
              const allViews = filteredTop.map(vv => Number(vv.views) || 0).filter(n => n > 0);
              const avgViews = allViews.length ? allViews.reduce((a, b) => a + b, 0) / allViews.length : 0;
              const outliers = filteredTop
                .map((v, i) => ({
                  index: i,
                  title: v.title || v.channelName || v.channel || 'Untitled',
                  views: Number(v.views),
                  multiplier: avgViews > 0 ? Number(v.views) / avgViews : 0
                }))
                .filter(v => v.views >= 3 * avgViews);
              if (!outliers.length) return null;
              const mostExtreme = outliers.reduce((a, b) => (a.multiplier > b.multiplier ? a : b));
              return (
                <div className="mb-4 px-4 py-3 rounded-xl bg-red-500/90 text-white font-bold text-center shadow-lg border border-red-400/60">
                  Outlier detected: <span className="underline">{mostExtreme.title}</span> got {mostExtreme.multiplier.toFixed(1)}x more views than average!
                </div>
              );
            })()}
            {(() => {
              // Add filter logic for top performers
              const filteredTop = (topFormatFilter && topFormatFilter !== 'All')
                ? contentSource.filter(v => getContentFormat(Number(v.duration)) === topFormatFilter)
                : contentSource;
              if (filteredTop.slice(0, 4).length === 0) {
                return (
                  <div className="flex flex-col items-center justify-center py-12 text-white/70">
                    <svg width="48" height="48" fill="none" viewBox="0 0 24 24" className="mb-2 text-yellow-300"><path fill="currentColor" d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20Zm0 3a7 7 0 1 1 0 14 7 7 0 0 1 0-14Zm0 2a5 5 0 1 0 0 10A5 5 0 0 0 12 7Zm0 2a3 3 0 1 1 0 6 3 3 0 0 1 0-6Z"/></svg>
                    <div className="text-lg font-semibold">No top performers found yet.</div>
                    <div className="text-sm text-white/50 mt-1">Try adjusting your filters or running a new analysis!</div>
                  </div>
                );
              }
              return (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {filteredTop.slice(0, 4).map((v, i) => {
                    // Try to get YouTube video ID from youtubeUrl
                    let videoId = '';
                    if (v.youtubeUrl) {
                      const match = String(v.youtubeUrl).match(/(?:v=|youtu\.be\/|embed\/)([\w-]{11})/);
                      if (match) videoId = match[1];
                    }
                    // Tags as array
                    let tags: string[] = [];
                    if (Array.isArray(v.tags)) tags = v.tags;
                    else if (typeof v.tags === 'string') {
                      try {
                        const parsed = JSON.parse(v.tags);
                        if (Array.isArray(parsed)) tags = parsed;
                        else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                      } catch {
                        tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                      }
                    }
                    // Outlier/highlight detection
                    const allViews = contentSource.map(vv => Number(vv.views) || 0).filter(n => n > 0);
                    const avgViews = allViews.length ? allViews.reduce((a, b) => a + b, 0) / allViews.length : 0;
                    const maxViews = allViews.length ? Math.max(...allViews) : 0;
                    const isTopVideo = Number(v.views) === maxViews;
                    const isOutlier = avgViews > 0 && Number(v.views) >= 3 * avgViews;
                    // Rank/emoji/multiplier
                    const rank = i + 1;
                    const multiplier = avgViews > 0 ? Number(v.views) / avgViews : 0;
                    let emoji = '🏆';
                    if (multiplier >= 3) emoji = '🔥';
                    else if (multiplier >= 2) emoji = '✨';
                    // Add icon/badge for format
                    const format = getContentFormat(Number(v.duration));
                    const formatIcon = format === 'Shorts' ? '🎬' : format === 'Short-form' ? '⏩' : format === 'Mid-form' ? '📺' : '🎥';
                    return (
                      <div
                        key={i}
                        className={`bg-white/5 rounded-lg p-4 flex flex-col gap-2 shadow border border-white/10 transition-all duration-300 cursor-pointer relative hover:ring-2 hover:ring-yellow-300/40 ${isTopVideo ? 'ring-4 ring-yellow-400/80 border-yellow-400/60 shadow-2xl scale-[1.03]' : ''}`}
                        onClick={() => { setModalIndex(i); setShowVideo(null); }}
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-2">
                            {/* Only show badge at top for rank 1 and 2 */}
                            {rank <= 2 && (
                              <span className="font-bold text-lg text-yellow-200 bg-yellow-400/20 rounded-full px-3 py-1 flex items-center gap-1 shadow border border-yellow-300/30">
                                {rank}
                                <span>{emoji}</span>
                                <span className="text-yellow-400 font-bold">{multiplier.toFixed(1)}x</span>
                              </span>
                            )}
                            <span className="text-lg font-bold text-yellow-300 flex items-center gap-2">
                              {v.channelName || v.channel || 'Channel'}
                              {isTopVideo && (
                                <span className="ml-2 px-2 py-1 rounded bg-yellow-400/80 text-white text-xs font-bold shadow">TOP VIDEO</span>
                              )}
                              {isOutlier && !isTopVideo && (
                                <span className="ml-2 px-2 py-1 rounded bg-red-500/80 text-white text-xs font-bold shadow">OUTLIER</span>
                              )}
                            </span>
                          </div>
                          {v.youtubeUrl && (
                            <button
                              className="p-2 rounded-lg hover:bg-red-500/10 transition-colors text-gray-400 hover:text-red-400"
                              title="Delete video"
                              onClick={e => { e.stopPropagation(); handleRemoveVideo(v.youtubeUrl); }}
                            >
                              <Trash2 className="w-5 h-5" />
                            </button>
                          )}
                        </div>
                        <div className="text-white/80 font-semibold flex items-center gap-2">
                          <span>{formatIcon}</span>
                          {v.title || 'Sample Video Title'}
                        </div>
                        <div className="flex gap-4 text-white/70 text-sm">
                          <span>Views: {v.views}</span>
                          <span>Likes: {v.likes}</span>
                          <span>Comments: {v.comments}</span>
                        </div>
                        {/* Outlier callout for TOP VIDEO with dynamic ranks/visuals */}
                        {isTopVideo && (() => {
                          const multiplier = Number(v.views) / avgViews;
                          let icon = null;
                          let bg = '';
                          let msg = '';
                          let iconClass = 'mb-2';
                          if (multiplier < 2) {
                            // Static trophy
                            icon = (
                              <span className="text-5xl" role="img" aria-label="Trophy">🏆</span>
                            );
                            bg = 'bg-yellow-200/80';
                            msg = `Solid performer! This video got ${multiplier.toFixed(1)}x more views than average.`;
                          } else if (multiplier < 3) {
                            // Trophy with sparkle
                            icon = (
                              <span className="relative flex items-center justify-center text-5xl">
                                <span role="img" aria-label="Trophy">🏆</span>
                                <span className="absolute -top-2 -right-2 text-yellow-400 text-2xl animate-ping">✨</span>
                              </span>
                            );
                            bg = 'bg-gradient-to-r from-yellow-300 via-yellow-400 to-yellow-500';
                            msg = `Amazing! This video got ${multiplier.toFixed(1)}x more views than average!`;
                          } else {
                            // Animated fire
                            icon = (
                              <span className="flex items-center justify-center text-6xl animate-bounce" style={{lineHeight:1}} role="img" aria-label="Fire">🔥</span>
                            );
                            bg = 'bg-gradient-to-r from-yellow-400 via-orange-400 to-red-500 animate-pulse-slow';
                            msg = `Incredible outlier! This video got ${multiplier.toFixed(1)}x more views than average!`;
                            iconClass += ' drop-shadow-lg';
                          }
                          return (
                            <div className={`mt-4 flex flex-col items-center justify-center`}>
                              <div className={iconClass}>{icon}</div>
                              <div className={`px-4 py-3 rounded-xl ${bg} text-white font-extrabold text-lg shadow-lg border-2 border-yellow-300/60 text-center`}>
                                {msg}
                              </div>
                            </div>
                          );
                        })()}
                        {/* For rank 3 or 4, show badge at bottom right */}
                        {rank > 2 && rank < 5 && (
                          <span className="absolute bottom-3 right-3 font-bold text-base text-yellow-200 bg-yellow-400/20 rounded-full px-3 py-1 flex items-center gap-1 shadow border border-yellow-300/30 z-10">
                            {rank}
                            <span>{emoji}</span>
                            <span className="text-yellow-400 font-bold">{multiplier.toFixed(1)}x</span>
                          </span>
                        )}
                      </div>
                    );
                  })}
                </div>
              );
            })()}
            {
              // Modal variables
              (modalIndex !== null && (() => {
                const v = contentSource[modalIndex];
                let videoId = '';
                if (v.youtubeUrl) {
                  const match = String(v.youtubeUrl).match(/(?:v=|youtu\.be\/|embed\/)([\w-]{11})/);
                  if (match) videoId = match[1];
                }
                let tags: string[] = [];
                if (Array.isArray(v.tags)) tags = v.tags;
                else if (typeof v.tags === 'string') {
                  try {
                    const parsed = JSON.parse(v.tags);
                    if (Array.isArray(parsed)) tags = parsed;
                    else tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                  } catch {
                    tags = String(v.tags).split(',').map((t: string) => t.trim()).filter(Boolean);
                  }
                }
                // Helper to format percentages to 4 digits after the point
                const formatPercent = (val: any) => {
                  const num = Number(val);
                  if (isNaN(num)) return val;
                  return num.toFixed(4) + '%';
                };
                // Helper to detect if video is vertical (shorts)
                const isVertical = v.youtubeUrl && (v.youtubeUrl.includes('shorts') || (v.width && v.height && v.height > v.width));
                const modalMaxW = isVertical ? 'max-w-lg' : 'max-w-2xl';
                const videoMaxW = isVertical ? 400 : 900;
                return ReactDOM.createPortal(
                  <div className="fixed inset-0 z-50 flex items-center justify-center">
                    {/* Overlay with strong blur and dark background */}
                    <div
                      className="absolute inset-0 bg-black/80 backdrop-blur-[8px] transition-all duration-300"
                      onClick={() => setModalIndex(null)}
                    />
                    <div
                      className={`relative bg-gradient-to-br from-[#3a2c1a]/95 to-[#2d1a1a]/95 rounded-2xl px-6 py-8 ${modalMaxW} w-full shadow-2xl border border-white/20 animate-fade-in flex flex-col items-center overflow-y-auto max-h-[90vh]`}
                      style={{ minWidth: 320, maxWidth: videoMaxW }}
                      onClick={e => e.stopPropagation()}
                    >
                      <button
                        className="absolute top-3 right-3 text-white/80 hover:text-yellow-300 text-2xl font-bold z-10"
                        onClick={() => setModalIndex(null)}
                        aria-label="Close"
                      >
                        ×
                      </button>
                      <div className="text-lg font-bold text-yellow-300 mb-1 w-full text-left">{v.channelName || v.channel || 'Channel'}</div>
                      <div className="text-white/90 font-semibold mb-2 w-full text-left">{v.title || 'Sample Video Title'}</div>
                      <div className="flex gap-6 text-white/90 text-base mb-4 w-full flex-wrap items-center justify-start">
                        <span className="flex items-center gap-1 font-bold">
                          <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="#facc15" d="M12 4.5c-7 0-10 7.5-10 7.5s3 7.5 10 7.5 10-7.5 10-7.5-3-7.5-10-7.5Zm0 13c-4.97 0-7.74-4.13-8.7-5.5C4.26 10.13 7.03 6 12 6s7.74 4.13 8.7 5.5c-.96 1.37-3.73 5.5-8.7 5.5Zm0-9A3.5 3.5 0 1 0 12 15a3.5 3.5 0 0 0 0-7Zm0 5A1.5 1.5 0 1 1 12 9a1.5 1.5 0 0 1 0 3Z"/></svg>
                          {v.views !== undefined && v.views !== null ? Number(v.views).toLocaleString() : '-'}
                        </span>
                        <span className="flex items-center gap-1 font-bold">
                          <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="#f87171" d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41 1.01 4.5 2.09C13.09 4.01 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35Z"/></svg>
                          {v.likes !== undefined && v.likes !== null ? Number(v.likes).toLocaleString() : '-'}
                        </span>
                        <span className="flex items-center gap-1 font-bold">
                          <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="#60a5fa" d="M21 6.5a2.5 2.5 0 0 0-2.5-2.5h-13A2.5 2.5 0 0 0 3 6.5v11A2.5 2.5 0 0 0 5.5 20h13a2.5 2.5 0 0 0 2.5-2.5v-11Zm-2.5-1a1 1 0 0 1 1 1V7h-15v-.5a1 1 0 0 1 1-1h13ZM20 8v9.5a1 1 0 0 1-1 1h-13a1 1 0 0 1-1-1V8h15Zm-7.5 2.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm-5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm10 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"/></svg>
                          {v.comments !== undefined && v.comments !== null ? Number(v.comments).toLocaleString() : '-'}
                        </span>
                      </div>
                      {/* YouTube embed or thumbnail */}
                      {videoId ? (
                        <div className="mb-3 flex flex-col items-center w-full justify-center">
                          {showVideo === modalIndex ? (
                            <div className="w-full flex justify-center">
                              <iframe
                                style={{ width: '100%', height: isVertical ? '600px' : '480px', minHeight: isVertical ? '500px' : '360px', maxWidth: isVertical ? '400px' : '900px', borderRadius: '1rem', boxShadow: '0 4px 32px 0 #0008' }}
                                src={`https://www.youtube.com/embed/${videoId}`}
                                title="YouTube video player"
                                frameBorder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                allowFullScreen
                              ></iframe>
                            </div>
                          ) : (
                            <div className="w-full flex flex-col items-center justify-center">
                              <img
                                src={`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`}
                                alt="Video thumbnail"
                                className="rounded-lg shadow-lg border border-white/20 cursor-pointer hover:brightness-110 transition"
                                onClick={e => { e.stopPropagation(); setShowVideo(modalIndex); }}
                                style={{ width: '100%', maxWidth: isVertical ? '400px' : '900px', minHeight: isVertical ? '500px' : '360px', objectFit: 'cover' }}
                              />
                              <button
                                className="mt-2 px-4 py-1 rounded bg-gradient-to-r from-yellow-400 to-red-400 text-white font-bold shadow hover:scale-105 transition"
                                onClick={e => { e.stopPropagation(); setShowVideo(modalIndex); }}
                              >
                                ▶ Watch in Card
                              </button>
                            </div>
                          )}
                        </div>
                      ) : null}
                      {/* Tags */}
                      {tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-2 w-full justify-center">
                          {tags.map((tag, idx) => (
                            <span key={idx} className="px-2 py-1 rounded-full bg-yellow-400/20 text-yellow-300 text-xs font-bold shadow border border-yellow-300/30 mb-1">{tag}</span>
                          ))}
                        </div>
                      )}
                      {/* Description */}
                      {v.description && (
                        <div className="text-white/80 text-sm bg-white/10 rounded p-3 border border-white/10 shadow-inner mb-2 w-full">
                          {v.description}
                        </div>
                      )}
                      {/* Other values */}
                      <div className="flex flex-wrap gap-4 text-white/70 text-xs mt-2 w-full justify-center">
                        {v.publishedDate && <span>Published: {new Date(v.publishedDate).toLocaleDateString()}</span>}
                        {v.engagementRate && <span>Engagement: {formatPercent(v.engagementRate)}</span>}
                        {v.viewsPerHour !== undefined && v.viewsPerHour !== null && <span>Views/Hour: {Math.floor(Number(v.viewsPerHour)).toLocaleString()}</span>}
                        {v.likeRate && <span>Like Rate: {formatPercent(v.likeRate)}</span>}
                        {v.commentRate && <span>Comment Rate: {formatPercent(v.commentRate)}</span>}
                        {v.duration && <span>Duration: {v.duration} sec</span>}
                      </div>
                    </div>
                  </div>,
                  document.body
                );
              })())
            }
          </>
        )}
        {activeTab === 4 && (
          <div className="flex flex-col items-center justify-center min-h-[40vh] bg-white/5 rounded-xl p-8 border border-white/10 shadow-lg text-center">
            <h3 className="text-2xl font-bold text-yellow-300 mb-4">Content Repurposing Ideas</h3>
            <p className="text-white/80 text-lg mb-4">
              Suggest ways to repurpose top-performing videos into other formats:
              <br />
              Turn a long video into a TikTok/Shorts series.
              <br />
            </p>
            <p className="text-white/70 italic mb-6">
              AI will find these moments for repurposing.
            </p>
            <div className="text-yellow-400 text-4xl font-extrabold animate-pulse">Coming Soon!</div>
          </div>
        )}
      </div>
      {/* Undo Toast Popup */}
      {lastDeletedVideo && (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end animate-slide-up">
          <div className="bg-yellow-900/95 border border-yellow-400/40 rounded-xl shadow-lg px-6 py-4 flex items-center gap-4 min-w-[260px] max-w-xs">
            <div className="flex-1">
              <div className="font-bold text-yellow-200 mb-1">Video deleted</div>
              <button
                className="text-yellow-300 underline font-semibold hover:text-yellow-400 transition"
                onClick={handleUndoDelete}
              >
                Undo ({undoTimer}s)
              </button>
            </div>
            <Trash2 className="w-6 h-6 text-yellow-400" />
          </div>
          <div className="w-full h-1 bg-yellow-400/30 rounded-b-xl overflow-hidden mt-1">
            <div
              className="h-1 bg-yellow-400 transition-all duration-1000"
              style={{ width: `${(undoTimer / 10) * 100}%` }}
            />
          </div>
        </div>
      )}
      {activeTab === 3 && (
        <ABTestPanel />
      )}
      {/* Add global scrollbar styles for yellow/orange palette */}
      <style>{`
        /* For all scrollbars */
        ::-webkit-scrollbar {
          height: 10px;
          width: 10px;
          background: #2d1a1a;
          border-radius: 8px;
        }
        ::-webkit-scrollbar-thumb {
          background: linear-gradient(90deg, #facc15 0%, #f59e42 100%);
          border-radius: 8px;
          border: 2px solid #2d1a1a;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(90deg, #f59e42 0%, #facc15 100%);
        }
        /* For horizontal tag scrolls */
        .tag-scroll::-webkit-scrollbar {
          height: 12px;
          background: #3a2c1a;
        }
        .tag-scroll::-webkit-scrollbar-thumb {
          background: linear-gradient(90deg, #facc15 0%, #f59e42 100%);
          border-radius: 8px;
          border: 2px solid #3a2c1a;
        }
      `}</style>
    </div>
  );
};

export default AnalysisDashboard; 