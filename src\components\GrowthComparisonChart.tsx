import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface ChartData {
  name: string;
  YouTube: number;
  TikTok: number;
  Instagram: number;
}

const data: ChartData[] = [
  { name: 'Jan', YouTube: 6500, TikTok: 13000, Instagram: 16000 },
  { name: 'Feb', YouTube: 7000, TikTok: 14000, Instagram: 18000 },
  { name: 'Mar', YouTube: 8000, TikTok: 15500, Instagram: 20000 },
  { name: 'Apr', YouTube: 9000, TikTok: 17000, Instagram: 22000 },
  { name: 'May', YouTube: 10000, TikTok: 18500, Instagram: 24000 },
  { name: 'Jun', YouTube: 11000, TikTok: 20000, Instagram: 25000 },
];

const GrowthComparisonChart: React.FC = () => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart
        data={data}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#ffffff20" />
        <XAxis dataKey="name" stroke="#ffffff" />
        <YAxis stroke="#ffffff" />
        <Tooltip
          contentStyle={{
            backgroundColor: 'rgba(255,255,255,0.1)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '8px',
            backdropFilter: 'blur(10px)',
          }}
          labelStyle={{ color: '#fff' }}
          itemStyle={{ color: '#fff' }}
        />
        <Line type="monotone" dataKey="YouTube" stroke="#ef4444" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
        <Line type="monotone" dataKey="TikTok" stroke="#f97316" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
        <Line type="monotone" dataKey="Instagram" stroke="#ec4899" strokeWidth={2} dot={{ r: 4 }} activeDot={{ r: 6 }} />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default GrowthComparisonChart; 