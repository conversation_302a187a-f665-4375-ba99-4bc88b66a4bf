import React, { useState, useEffect, useCallback } from 'react';
import { Link as LinkIcon, Trash2, Calendar, ExternalLink } from 'lucide-react';
import { SavedLink } from '../types';
import { getSavedLinks, removeLink, cleanupExpiredLinks } from '../utils/storage';

interface SavedLinksProps {
  onShowToast: (type: 'success' | 'error' | 'warning' | 'info', message: string) => void;
}

const SavedLinks: React.FC<SavedLinksProps> = ({ onShowToast }) => {
  const [savedLinks, setSavedLinks] = useState<SavedLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const loadLinks = useCallback(() => {
    setIsLoading(true);
    const expiredCount = cleanupExpiredLinks();
    if (expiredCount > 0) {
      onShowToast('info', `Cleaned up ${expiredCount} expired link${expiredCount > 1 ? 's' : ''}`);
    }

    const links = getSavedLinks();
    setSavedLinks(links);
    setIsLoading(false);
  }, [onShowToast]);

  useEffect(() => {
    loadLinks();
  }, [loadLinks]);

  const handleDelete = useCallback(async (id: string) => {
    setDeletingId(id);
    
    try {
      removeLink(id);
      setSavedLinks(prev => prev.filter(link => link.id !== id));
      onShowToast('success', 'Link deleted successfully');
    } catch {
      onShowToast('error', 'Failed to delete link');
    } finally {
      setDeletingId(null);
    }
  }, [onShowToast]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-pulse space-y-4 w-full max-w-2xl">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white/5 rounded-lg p-6">
              <div className="h-4 bg-white/10 rounded w-3/4"></div>
              <div className="h-3 bg-white/10 rounded w-1/2 mt-4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (savedLinks.length === 0) {
    return (
      <div className="text-center py-12">
        <LinkIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-300">No saved links</h3>
        <p className="mt-2 text-sm text-gray-400">
          Your saved analysis links will appear here
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-4">
          Saved Analysis Links
        </h2>
        <p className="text-gray-400">
          Access your previous YouTube content analysis results
        </p>
      </div>

      <div className="space-y-4">
        {savedLinks.map(link => (
          <div
            key={link.id}
            className="group bg-white/5 backdrop-blur-sm rounded-lg p-6 transition-all duration-300
                     hover:bg-white/10 border border-white/10 hover:border-white/20"
          >
            <div className="flex items-start justify-between">
              <div className="space-y-1 flex-1">
                <h3 className="font-medium text-white group-hover:text-red-400 transition-colors">
                  {link.title}
                </h3>
                
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <Calendar className="w-4 h-4" />
                  <span>Created: {new Date(link.createdAt).toLocaleDateString()}</span>
                  <span className="text-gray-500">•</span>
                  <span>Expires: {new Date(link.expiresAt).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <a
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-lg hover:bg-white/10 transition-colors text-gray-400 hover:text-white"
                  title="Open analysis"
                >
                  <ExternalLink className="w-5 h-5" />
                </a>
                
                <button
                  onClick={() => handleDelete(link.id)}
                  disabled={deletingId === link.id}
                  className="p-2 rounded-lg hover:bg-red-500/10 transition-colors text-gray-400 hover:text-red-400"
                  title="Delete link"
                >
                  <Trash2 className={`w-5 h-5 ${deletingId === link.id ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SavedLinks;