import React, { useMemo, useState } from 'react';

// Minimal subset of fields we need. Keep it flexible because the dashboard data
// is user-populated and may have missing values.
export interface VideoRow {
  youtubeUrl?: string;
  title?: string;
  channelName?: string;
  channel?: string;
  duration?: number | string; // seconds or string
  views?: number;
  likes?: number;
  comments?: number;
  engagementRate?: number; // already a percentage (0-1 or 0-100)
  viewsPerHour?: number;
  tags?: string[] | string;
  publishedDate?: string;
  [key: string]: any;
}

const metricDefs: { key: keyof VideoRow; label: string; formatter?: (v: any) => string }[] = [
  { key: 'views', label: 'Views', formatter: (v) => Number(v || 0).toLocaleString() },
  { key: 'likes', label: 'Likes', formatter: (v) => Number(v || 0).toLocaleString() },
  { key: 'comments', label: 'Comments', formatter: (v) => Number(v || 0).toLocaleString() },
  {
    key: 'engagementRate',
    label: 'Engagement Rate',
    formatter: (v) => {
      if (v === null || v === undefined) return '-';
      let n = Number(v);
      // If the number is less than 1, it's a decimal that needs to be converted to percentage
      // Otherwise, it's already a percentage
      const percentage = n < 1 ? n * 100 : n;
      return percentage.toFixed(2) + '%';
    },
  },
  { key: 'viewsPerHour', label: 'Views / Hour', formatter: (v) => Number(v || 0).toLocaleString() },
  {
    key: 'duration',
    label: 'Duration',
    formatter: (v) => {
      const n = Number(v);
      if (isNaN(n)) return '-';
      const m = Math.floor(n / 60);
      const s = Math.round(n % 60);
      return `${m}m ${s}s`;
    },
  },
];

const parseTags = (t: VideoRow['tags']): string[] => {
  if (!t) return [];
  if (Array.isArray(t)) return t;
  try {
    const parsed = JSON.parse(t);
    if (Array.isArray(parsed)) return parsed;
  } catch (_) {}
  return String(t)
    .split(',')
    .map((tag) => tag.trim())
    .filter(Boolean);
};

const ABTestPanel: React.FC = () => {
  const [selectedA, setSelectedA] = useState<string>('');
  const [selectedB, setSelectedB] = useState<string>('');

  // Pull dashboard data once
  const videos: VideoRow[] = useMemo(() => {
    try {
      const raw = localStorage.getItem('dashboardData');
      if (!raw) return [];
      return JSON.parse(raw);
    } catch {
      return [];
    }
  }, []);

  const videoA = useMemo(() => videos.find((v) => v.youtubeUrl === selectedA), [videos, selectedA]);
  const videoB = useMemo(() => videos.find((v) => v.youtubeUrl === selectedB), [videos, selectedB]);

  const overlapTags = useMemo(() => {
    if (!videoA || !videoB) return [] as string[];
    const setA = new Set(parseTags(videoA.tags));
    const setB = new Set(parseTags(videoB.tags));
    return [...setA].filter((t) => setB.has(t));
  }, [videoA, videoB]);

  const uniqueATags = useMemo(() => {
    if (!videoA) return [] as string[];
    const A = parseTags(videoA.tags);
    if (!videoB) return A;
    const overlap = new Set(overlapTags);
    return A.filter((t) => !overlap.has(t));
  }, [videoA, videoB, overlapTags]);

  const uniqueBTags = useMemo(() => {
    if (!videoB) return [] as string[];
    const B = parseTags(videoB.tags);
    if (!videoA) return B;
    const overlap = new Set(overlapTags);
    return B.filter((t) => !overlap.has(t));
  }, [videoA, videoB, overlapTags]);

  // Simple insight: which video "wins" each metric (higher is better, except duration we consider closer?)
  const winnerForMetric = (key: keyof VideoRow): 'A' | 'B' | null => {
    if (!videoA || !videoB) return null;
    const aVal = Number(videoA[key] ?? 0);
    const bVal = Number(videoB[key] ?? 0);
    if (isNaN(aVal) || isNaN(bVal)) return null;
    if (key === 'duration') {
      // For duration, shorter may be better? We'll skip winner.
      return null;
    }
    return aVal === bVal ? null : aVal > bVal ? 'A' : 'B';
  };

  const predictiveInsight = useMemo(() => {
    if (!videoA || !videoB) return '';
    const erA = Number(videoA.engagementRate || 0);
    const erB = Number(videoB.engagementRate || 0);
    const vphA = Number(videoA.viewsPerHour || 0);
    const vphB = Number(videoB.viewsPerHour || 0);

    if (erA === erB && vphA === vphB) return 'Both videos perform similarly based on the selected metrics.';
    const best = erA * vphA > erB * vphB ? 'A' : 'B';
    return best === 'A'
      ? `${videoA.title ?? 'Video A'} currently shows stronger momentum and engagement; it is likely to outperform ${videoB.title ?? 'Video B'} over the next 24 hours.`
      : `${videoB.title ?? 'Video B'} currently shows stronger momentum and engagement; it is likely to outperform ${videoA.title ?? 'Video A'} over the next 24 hours.`;
  }, [videoA, videoB]);

  return (
    <div className="bg-white/5 border border-white/10 rounded-xl p-6 shadow-lg text-white">
      <h2 className="text-2xl font-bold text-yellow-300 mb-6 text-center">A/B Testing</h2>

      {/* Selection Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <div>
          <label className="block text-sm mb-1 font-semibold text-white/80">Video A</label>
          <select
            className="w-full bg-[#2d1a1a] border border-white/20 rounded-lg px-3 py-2 text-white/90 focus:outline-none"
            value={selectedA}
            onChange={(e) => setSelectedA(e.target.value)}
          >
            <option value="">-- Select a video --</option>
            {videos.map((v) => (
              <option key={v.youtubeUrl} value={v.youtubeUrl}>
                {v.title?.slice(0, 80) || v.youtubeUrl}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm mb-1 font-semibold text-white/80">Video B</label>
          <select
            className="w-full bg-[#2d1a1a] border border-white/20 rounded-lg px-3 py-2 text-white/90 focus:outline-none"
            value={selectedB}
            onChange={(e) => setSelectedB(e.target.value)}
          >
            <option value="">-- Select a video --</option>
            {videos.map((v) => (
              <option key={v.youtubeUrl} value={v.youtubeUrl}>
                {v.title?.slice(0, 80) || v.youtubeUrl}
              </option>
            ))}
          </select>
        </div>
      </div>

      {videoA && videoB ? (
        <>
          {/* Metrics Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead>
                <tr>
                  <th className="py-2 px-3 text-left font-bold">Metric</th>
                  <th className="py-2 px-3 text-left font-bold text-yellow-300 w-48">Video A</th>
                  <th className="py-2 px-3 text-left font-bold text-yellow-300 w-48">Video B</th>
                </tr>
              </thead>
              <tbody>
                {metricDefs.map(({ key, label, formatter }) => {
                  const win = winnerForMetric(key);
                  const aVal = formatter ? formatter(videoA[key]) : String(videoA[key] ?? '-');
                  const bVal = formatter ? formatter(videoB[key]) : String(videoB[key] ?? '-');
                  return (
                    <tr key={String(key)} className="border-t border-white/10">
                      <td className="py-2 px-3 font-semibold text-white/80">{label}</td>
                      <td className={`py-2 px-3 ${win === 'A' ? 'bg-green-700/40' : ''}`}>{aVal}</td>
                      <td className={`py-2 px-3 ${win === 'B' ? 'bg-green-700/40' : ''}`}>{bVal}</td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Tag overlap */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-10">
            <div>
              <h3 className="font-bold text-yellow-300 mb-2 text-center">Unique Tags — Video A</h3>
              {uniqueATags.length ? (
                <div className="flex flex-wrap gap-2 justify-center">
                  {uniqueATags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-white/10 rounded text-xs">
                      #{tag}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-center text-white/60">None</p>
              )}
            </div>
            <div>
              <h3 className="font-bold text-yellow-300 mb-2 text-center">Overlapping Tags</h3>
              {overlapTags.length ? (
                <div className="flex flex-wrap gap-2 justify-center">
                  {overlapTags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-green-600/40 rounded text-xs">
                      #{tag}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-center text-white/60">None</p>
              )}
            </div>
            <div>
              <h3 className="font-bold text-yellow-300 mb-2 text-center">Unique Tags — Video B</h3>
              {uniqueBTags.length ? (
                <div className="flex flex-wrap gap-2 justify-center">
                  {uniqueBTags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-white/10 rounded text-xs">
                      #{tag}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-center text-white/60">None</p>
              )}
            </div>
          </div>

          {/* Predictive insight */}
          {predictiveInsight && (
            <div className="mt-10 bg-blue-900/40 border border-blue-400/60 rounded-xl p-4 text-center text-white/90 shadow">
              {predictiveInsight}
            </div>
          )}
        </>
      ) : (
        <p className="text-center text-white/60">Select two videos to begin the comparison.</p>
      )}
    </div>
  );
};

export default ABTestPanel;
